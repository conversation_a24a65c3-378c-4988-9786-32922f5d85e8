#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查用户权限
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app
    from app.models import db
    from app.models.user import User
    from app.models.menu import MenuItem
    print("✅ 模块导入成功")
except Exception as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

def check_permissions():
    """检查用户权限"""
    app = create_app()
    
    with app.app_context():
        try:
            print("=== 检查用户权限 ===")
            
            # 检查所有用户
            users = User.query.all()
            print(f"\n所有用户数量: {len(users)}")
            
            for user in users:
                print(f"\n用户: {user.username} ({user.real_name or '未设置'})")
                print(f"  是否活跃: {user.is_active}")
                print(f"  是否管理员: {user.is_admin}")
                print(f"  角色: {[role.name for role in user.roles]}")
                
                # 检查菜单权限
                menu_items = user.get_menu_items()
                print(f"  可访问菜单数量: {len(menu_items)}")
                for menu in menu_items:
                    print(f"    - {menu.name} ({menu.url})")
                
                # 检查特定权限
                content_permission = any(menu.url == '/simple/content' for menu in menu_items)
                print(f"  有内容生成权限: {content_permission}")
            
            # 检查所有菜单项
            print(f"\n=== 所有菜单项 ===")
            all_menus = MenuItem.query.filter_by(is_active=True).order_by(MenuItem.order).all()
            for menu in all_menus:
                print(f"  {menu.name} - {menu.url} - 权限: {menu.permission}")
            
            print("\n=== 检查完成 ===")
            
        except Exception as e:
            print(f"❌ 检查过程中出错: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    check_permissions()
