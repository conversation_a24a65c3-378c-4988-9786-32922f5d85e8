#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
添加编辑人员分配字段到contents表
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models import db
import pymysql

def add_editor_fields():
    """添加编辑人员分配字段"""
    app = create_app()
    
    with app.app_context():
        try:
            # 获取数据库连接
            connection = db.engine.raw_connection()
            cursor = connection.cursor()
            
            print("开始添加编辑人员分配字段...")
            
            # 检查字段是否已存在
            cursor.execute("""
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = 'xhsrw666' 
                AND TABLE_NAME = 'contents' 
                AND COLUMN_NAME IN ('image_editor_id', 'content_editor_id')
            """)
            existing_columns = [row[0] for row in cursor.fetchall()]
            
            if 'image_editor_id' in existing_columns:
                print("字段 image_editor_id 已存在，跳过添加")
            else:
                # 添加图片编辑管理员ID字段
                cursor.execute("""
                    ALTER TABLE contents 
                    ADD COLUMN image_editor_id INT NULL COMMENT '图片编辑管理员ID'
                """)
                print("✅ 添加字段 image_editor_id 成功")
            
            if 'content_editor_id' in existing_columns:
                print("字段 content_editor_id 已存在，跳过添加")
            else:
                # 添加文案编辑管理员ID字段
                cursor.execute("""
                    ALTER TABLE contents 
                    ADD COLUMN content_editor_id INT NULL COMMENT '文案编辑管理员ID（初审）'
                """)
                print("✅ 添加字段 content_editor_id 成功")
            
            # 检查外键约束是否已存在
            cursor.execute("""
                SELECT CONSTRAINT_NAME 
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = 'xhsrw666' 
                AND TABLE_NAME = 'contents' 
                AND CONSTRAINT_NAME IN ('fk_contents_image_editor', 'fk_contents_content_editor')
            """)
            existing_constraints = [row[0] for row in cursor.fetchall()]
            
            if 'fk_contents_image_editor' not in existing_constraints:
                # 添加外键约束
                cursor.execute("""
                    ALTER TABLE contents 
                    ADD CONSTRAINT fk_contents_image_editor 
                    FOREIGN KEY (image_editor_id) REFERENCES users(id) ON DELETE SET NULL
                """)
                print("✅ 添加外键约束 fk_contents_image_editor 成功")
            else:
                print("外键约束 fk_contents_image_editor 已存在，跳过添加")
            
            if 'fk_contents_content_editor' not in existing_constraints:
                cursor.execute("""
                    ALTER TABLE contents 
                    ADD CONSTRAINT fk_contents_content_editor 
                    FOREIGN KEY (content_editor_id) REFERENCES users(id) ON DELETE SET NULL
                """)
                print("✅ 添加外键约束 fk_contents_content_editor 成功")
            else:
                print("外键约束 fk_contents_content_editor 已存在，跳过添加")
            
            # 检查索引是否已存在
            cursor.execute("""
                SELECT INDEX_NAME 
                FROM INFORMATION_SCHEMA.STATISTICS 
                WHERE TABLE_SCHEMA = 'xhsrw666' 
                AND TABLE_NAME = 'contents' 
                AND INDEX_NAME IN ('idx_contents_image_editor_id', 'idx_contents_content_editor_id')
            """)
            existing_indexes = [row[0] for row in cursor.fetchall()]
            
            if 'idx_contents_image_editor_id' not in existing_indexes:
                # 添加索引
                cursor.execute("""
                    CREATE INDEX idx_contents_image_editor_id ON contents(image_editor_id)
                """)
                print("✅ 添加索引 idx_contents_image_editor_id 成功")
            else:
                print("索引 idx_contents_image_editor_id 已存在，跳过添加")
            
            if 'idx_contents_content_editor_id' not in existing_indexes:
                cursor.execute("""
                    CREATE INDEX idx_contents_content_editor_id ON contents(content_editor_id)
                """)
                print("✅ 添加索引 idx_contents_content_editor_id 成功")
            else:
                print("索引 idx_contents_content_editor_id 已存在，跳过添加")
            
            # 提交事务
            connection.commit()
            
            # 验证字段是否添加成功
            cursor.execute("DESCRIBE contents")
            columns = cursor.fetchall()
            
            print("\n当前 contents 表结构:")
            for column in columns:
                if 'editor_id' in column[0]:
                    print(f"  {column[0]}: {column[1]} {column[2]} {column[3]} {column[4]} {column[5]}")
            
            print("\n✅ 所有字段添加完成！")
            
        except Exception as e:
            print(f"❌ 添加字段失败: {str(e)}")
            connection.rollback()
            raise
        finally:
            cursor.close()
            connection.close()

if __name__ == '__main__':
    add_editor_fields()
