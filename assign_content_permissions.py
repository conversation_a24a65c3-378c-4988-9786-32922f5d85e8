#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
给用户分配内容生成权限
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app
    from app.models import db
    from app.models.user import User
    from app.models.menu import MenuItem
    print("✅ 模块导入成功")
except Exception as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

def assign_content_permissions():
    """给用户分配内容生成权限"""
    app = create_app()
    
    with app.app_context():
        try:
            print("=== 给用户分配内容生成权限 ===")
            
            # 找到内容生成菜单项
            content_menu = MenuItem.query.filter_by(url='/simple/content').first()
            if not content_menu:
                print("❌ 找不到内容生成菜单项")
                return
            
            print(f"✅ 找到内容生成菜单: {content_menu.name} (权限: {content_menu.permission})")
            
            # 需要分配权限的用户列表（除了admin，因为admin已经是超级管理员）
            target_users = ['reviewer', 'editor', 'final_reviewer', 'publisher']
            
            for username in target_users:
                user = User.query.filter_by(username=username).first()
                if not user:
                    print(f"❌ 找不到用户: {username}")
                    continue
                
                # 检查用户是否已经有这个菜单权限
                if content_menu in user.menu_permissions:
                    print(f"✅ 用户 {username} 已经有内容生成权限")
                else:
                    # 添加菜单权限
                    user.menu_permissions.append(content_menu)
                    print(f"✅ 给用户 {username} 添加内容生成权限")
            
            # 提交更改
            db.session.commit()
            print("\n✅ 权限分配完成！")
            
            # 验证权限分配结果
            print("\n=== 验证权限分配结果 ===")
            for username in target_users:
                user = User.query.filter_by(username=username).first()
                if user:
                    menu_items = user.get_menu_items()
                    has_content_permission = any(menu.url == '/simple/content' for menu in menu_items)
                    print(f"用户 {username}: {'✅ 有' if has_content_permission else '❌ 没有'} 内容生成权限")
            
        except Exception as e:
            print(f"❌ 权限分配过程中出错: {str(e)}")
            db.session.rollback()
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    assign_content_permissions()
