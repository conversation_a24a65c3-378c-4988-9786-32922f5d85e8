#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试用户过滤功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app
    from app.models import db
    from app.models.user import User
    from app.models.content import Content
    from app.models.client import Client
    print("✅ 模块导入成功")
except Exception as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

def test_user_filtering():
    """测试用户过滤功能"""
    app = create_app()
    
    with app.app_context():
        try:
            print("=== 测试用户过滤功能 ===")
            
            # 获取所有用户
            users = User.query.filter_by(is_active=True).all()
            print(f"\n所有活跃用户数量: {len(users)}")
            
            for user in users:
                print(f"\n用户: {user.username} ({user.real_name or '未设置'})")
                print(f"  是否超级管理员: {'是' if user.has_role('超级管理员') else '否'}")
                
                # 统计分配给该用户的文案数量
                assigned_contents = Content.query.filter(
                    Content.content_editor_id == user.id,
                    Content.is_deleted == False
                ).all()
                
                print(f"  分配的文案数量: {len(assigned_contents)}")
                
                if assigned_contents:
                    print(f"  分配的文案ID: {[c.id for c in assigned_contents[:5]]}{'...' if len(assigned_contents) > 5 else ''}")
                    
                    # 统计不同状态的文案
                    draft_count = len([c for c in assigned_contents if c.workflow_status == 'draft'])
                    pending_count = len([c for c in assigned_contents if c.internal_review_status == 'rejected'])
                    
                    print(f"  草稿状态: {draft_count}")
                    print(f"  待审核状态: {pending_count}")
            
            # 测试客户统计
            print(f"\n=== 测试客户统计 ===")
            
            # 模拟不同用户的客户统计
            for user in users:
                if user.has_role('超级管理员'):
                    print(f"\n{user.username} (超级管理员) - 可以看到所有客户的文案")
                    # 超级管理员的查询逻辑
                    base_filters = [Content.is_deleted == False]
                else:
                    print(f"\n{user.username} (普通用户) - 只能看到分配给自己的文案")
                    # 普通用户的查询逻辑
                    base_filters = [Content.is_deleted == False, Content.content_editor_id == user.id]
                
                # 统计有文案的客户数量
                clients_with_content = db.session.query(Client.id, Client.name).join(
                    Content, Client.id == Content.client_id
                ).filter(*base_filters).distinct().all()
                
                print(f"  可见的客户数量: {len(clients_with_content)}")
                if clients_with_content:
                    client_names = [c.name for c in clients_with_content[:3]]
                    print(f"  客户示例: {client_names}{'...' if len(clients_with_content) > 3 else ''}")
            
            print("\n=== 测试完成 ===")
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_user_filtering()
