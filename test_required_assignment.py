#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试必填用户分配功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app
    from app.models import db
    from app.models.user import User
    from app.forms.content import GenerateContentForm
    from app.models.client import Client
    from app.models.template import TemplateCategory
    print("✅ 模块导入成功")
except Exception as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

def test_required_assignment():
    """测试必填用户分配功能"""
    app = create_app()
    
    with app.app_context():
        with app.test_request_context():
            try:
                print("=== 测试必填用户分配功能 ===")
                
                # 创建表单
                form = GenerateContentForm()
                
                # 初始化选择项
                form.client_id.choices = [(c.id, c.name) for c in Client.query.filter_by(status=True).all()]
                form.template_category_id.choices = [(c.id, c.name) for c in TemplateCategory.query.all()]
                
                # 获取活跃用户
                active_users = User.query.filter_by(is_active=True).all()
                print(f"\n所有活跃用户数量: {len(active_users)}")
                
                # 过滤有权限的用户
                content_editors = [u for u in active_users if u.has_permission('content.review')]
                image_editors = [u for u in active_users if u.has_permission('image.upload')]
                
                print(f"有初审权限的用户数量: {len(content_editors)}")
                print(f"有图片上传权限的用户数量: {len(image_editors)}")
                
                # 生成选择项（不包含"不分配"选项）
                content_editor_choices = [(u.id, f"{u.real_name or u.username}") for u in content_editors]
                image_editor_choices = [(u.id, f"{u.real_name or u.username}") for u in image_editors]
                
                form.content_editor_id.choices = content_editor_choices
                form.image_editor_id.choices = image_editor_choices
                
                # 设置默认值
                if content_editor_choices:
                    form.content_editor_id.data = content_editor_choices[0][0]
                    print(f"\n文案编辑管理员默认选择: {content_editor_choices[0][1]} (ID: {content_editor_choices[0][0]})")
                
                if image_editor_choices:
                    form.image_editor_id.data = image_editor_choices[0][0]
                    print(f"图片编辑管理员默认选择: {image_editor_choices[0][1]} (ID: {image_editor_choices[0][0]})")
                
                # 显示所有选择项
                print(f"\n文案编辑管理员选择项:")
                for choice_id, choice_name in content_editor_choices:
                    default_mark = " (默认)" if choice_id == form.content_editor_id.data else ""
                    print(f"  ID: {choice_id}, 名称: {choice_name}{default_mark}")
                
                print(f"\n图片编辑管理员选择项:")
                for choice_id, choice_name in image_editor_choices:
                    default_mark = " (默认)" if choice_id == form.image_editor_id.data else ""
                    print(f"  ID: {choice_id}, 名称: {choice_name}{default_mark}")
                
                # 测试表单验证
                print(f"\n=== 测试表单验证 ===")
                
                # 检查字段是否为必填
                content_editor_validators = [v.__class__.__name__ for v in form.content_editor_id.validators]
                image_editor_validators = [v.__class__.__name__ for v in form.image_editor_id.validators]
                
                print(f"文案编辑管理员字段验证器: {content_editor_validators}")
                print(f"图片编辑管理员字段验证器: {image_editor_validators}")
                
                has_required_content = 'DataRequired' in content_editor_validators
                has_required_image = 'DataRequired' in image_editor_validators
                
                print(f"文案编辑管理员是否必填: {'✅ 是' if has_required_content else '❌ 否'}")
                print(f"图片编辑管理员是否必填: {'✅ 是' if has_required_image else '❌ 否'}")
                
                # 测试HTML渲染
                print(f"\n=== 测试HTML渲染 ===")
                
                content_editor_html = str(form.content_editor_id)
                image_editor_html = str(form.image_editor_id)
                
                print(f"文案编辑选择框HTML长度: {len(content_editor_html)}")
                print(f"图片编辑选择框HTML长度: {len(image_editor_html)}")
                
                # 检查是否包含默认选择
                if content_editor_choices:
                    default_content_name = content_editor_choices[0][1]
                    if f'selected>{default_content_name}' in content_editor_html or f'selected="selected"' in content_editor_html:
                        print("✅ 文案编辑选择框包含默认选择")
                    else:
                        print("❌ 文案编辑选择框没有默认选择")
                
                if image_editor_choices:
                    default_image_name = image_editor_choices[0][1]
                    if f'selected>{default_image_name}' in image_editor_html or f'selected="selected"' in image_editor_html:
                        print("✅ 图片编辑选择框包含默认选择")
                    else:
                        print("❌ 图片编辑选择框没有默认选择")
                
                # 检查是否不包含"不分配"选项
                if '-- 不分配 --' not in content_editor_html:
                    print("✅ 文案编辑选择框不包含'不分配'选项")
                else:
                    print("❌ 文案编辑选择框仍包含'不分配'选项")
                
                if '-- 不分配 --' not in image_editor_html:
                    print("✅ 图片编辑选择框不包含'不分配'选项")
                else:
                    print("❌ 图片编辑选择框仍包含'不分配'选项")
                
                print("\n=== 测试完成 ===")
                
            except Exception as e:
                print(f"❌ 测试过程中出错: {str(e)}")
                import traceback
                traceback.print_exc()

if __name__ == '__main__':
    test_required_assignment()
