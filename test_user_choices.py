#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试用户选择项是否正确加载
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app
    from app.models import db
    from app.models.user import User
    from app.forms.content import GenerateContentForm
    from app.models.client import Client
    from app.models.template import TemplateCategory
    print("✅ 模块导入成功")
except Exception as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

def test_user_choices():
    """测试用户选择项加载"""
    app = create_app()
    
    with app.app_context():
        try:
            print("=== 测试用户选择项加载 ===")
            
            # 1. 检查活跃用户
            print("\n1. 检查活跃用户...")
            active_users = User.query.filter_by(is_active=True).all()
            print(f"✅ 找到 {len(active_users)} 个活跃用户:")
            for user in active_users:
                display_name = user.real_name or user.username
                print(f"   - ID: {user.id}, 显示名: {display_name}")
            
            # 2. 测试表单初始化
            print("\n2. 测试表单初始化...")
            form = GenerateContentForm()
            
            # 初始化客户选择项
            clients = Client.query.filter_by(status=True).all()
            form.client_id.choices = [(c.id, c.name) for c in clients]
            print(f"✅ 客户选择项: {len(form.client_id.choices)} 个")
            
            # 初始化模板分类选择项
            categories = TemplateCategory.query.all()
            form.template_category_id.choices = [(c.id, c.name) for c in categories]
            print(f"✅ 模板分类选择项: {len(form.template_category_id.choices)} 个")
            
            # 初始化用户选择项
            user_choices = [(0, '-- 不分配 --')] + [(u.id, f"{u.real_name or u.username}") for u in active_users]
            form.image_editor_id.choices = user_choices
            form.content_editor_id.choices = user_choices
            
            print(f"✅ 用户选择项: {len(user_choices)} 个")
            print("   用户选择项详情:")
            for choice_id, choice_name in user_choices:
                print(f"     - ID: {choice_id}, 名称: {choice_name}")
            
            # 3. 验证表单字段
            print("\n3. 验证表单字段...")
            print(f"✅ image_editor_id 字段存在: {hasattr(form, 'image_editor_id')}")
            print(f"✅ content_editor_id 字段存在: {hasattr(form, 'content_editor_id')}")
            
            if hasattr(form, 'image_editor_id'):
                print(f"   image_editor_id 选择项数量: {len(form.image_editor_id.choices)}")
            
            if hasattr(form, 'content_editor_id'):
                print(f"   content_editor_id 选择项数量: {len(form.content_editor_id.choices)}")
            
            # 4. 模拟渲染测试
            print("\n4. 模拟渲染测试...")
            try:
                # 测试字段渲染
                image_editor_html = str(form.image_editor_id)
                content_editor_html = str(form.content_editor_id)
                
                print("✅ 字段渲染成功")
                print(f"   image_editor_id HTML长度: {len(image_editor_html)}")
                print(f"   content_editor_id HTML长度: {len(content_editor_html)}")
                
                # 检查是否包含用户选项
                for user in active_users[:3]:  # 只检查前3个用户
                    display_name = user.real_name or user.username
                    if display_name in image_editor_html:
                        print(f"   ✅ 图片编辑选择框包含用户: {display_name}")
                    else:
                        print(f"   ❌ 图片编辑选择框缺少用户: {display_name}")
                        
                    if display_name in content_editor_html:
                        print(f"   ✅ 文案编辑选择框包含用户: {display_name}")
                    else:
                        print(f"   ❌ 文案编辑选择框缺少用户: {display_name}")
                
            except Exception as e:
                print(f"❌ 字段渲染失败: {e}")
            
            print("\n=== 测试完成 ===")
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_user_choices()
