#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试用户添加功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app
    from app.models import db
    from app.models.user import User, Permission
    from app.models.menu import MenuItem
    import json
    print("✅ 模块导入成功")
except Exception as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

def test_add_user():
    """测试用户添加功能"""
    app = create_app()
    
    with app.app_context():
        try:
            print("=== 测试用户添加功能 ===")
            
            # 模拟用户添加的数据
            test_data = {
                'username': 'test_user_123',
                'email': '<EMAIL>',
                'password': 'test123456',
                'real_name': '测试用户',
                'phone': '13800138000',
                'is_active': True,
                'content_generate': True,
                'menu_permissions': []
            }
            
            print(f"测试数据: {json.dumps(test_data, ensure_ascii=False, indent=2)}")
            
            # 检查必需字段
            required_fields = ['username', 'email', 'password']
            for field in required_fields:
                if field not in test_data or not test_data[field]:
                    print(f"❌ 缺少必需字段: {field}")
                    return
            
            print("✅ 必需字段检查通过")
            
            # 检查用户名和邮箱是否已存在
            existing_user = User.query.filter_by(username=test_data['username']).first()
            if existing_user:
                print(f"❌ 用户名已存在: {test_data['username']}")
                return
            
            existing_email = User.query.filter_by(email=test_data['email']).first()
            if existing_email:
                print(f"❌ 邮箱已存在: {test_data['email']}")
                return
            
            print("✅ 用户名和邮箱检查通过")
            
            # 模拟创建用户的过程
            print("\n=== 模拟创建用户过程 ===")
            
            # 创建新用户
            user = User(
                username=test_data['username'],
                email=test_data['email'],
                real_name=test_data.get('real_name', ''),
                phone=test_data.get('phone', ''),
                is_active=test_data.get('is_active', True)
            )
            user.password = test_data['password']
            print(f"✅ 用户对象创建成功: {user.username}")
            
            # 处理权限
            if test_data.get('content_generate', False):
                print("处理content_generate权限...")
                perm = Permission.query.filter_by(name='content.generate').first()
                if not perm:
                    perm = Permission(name='content.generate', description='内容生成权限')
                    db.session.add(perm)
                    db.session.flush()
                    print("✅ 创建content.generate权限")
                else:
                    print("✅ 找到现有content.generate权限")
                
                if perm not in user.permissions:
                    user.permissions.append(perm)
                    print("✅ 权限添加到用户")
            
            # 处理菜单权限
            if 'menu_permissions' in test_data and test_data['menu_permissions']:
                menu_items = MenuItem.query.filter(MenuItem.id.in_(test_data['menu_permissions'])).all()
                user.menu_permissions = menu_items
                print(f"✅ 菜单权限设置完成: {len(menu_items)} 个菜单")
            else:
                print("✅ 无菜单权限需要设置")
            
            # 尝试保存到数据库
            print("\n=== 尝试保存到数据库 ===")
            db.session.add(user)
            db.session.commit()
            print("✅ 用户保存成功")
            
            # 验证用户创建
            created_user = User.query.filter_by(username=test_data['username']).first()
            if created_user:
                print(f"✅ 用户创建验证成功: ID={created_user.id}")
                print(f"  用户名: {created_user.username}")
                print(f"  邮箱: {created_user.email}")
                print(f"  真实姓名: {created_user.real_name}")
                print(f"  是否活跃: {created_user.is_active}")
                print(f"  权限数量: {len(created_user.permissions)}")
                print(f"  菜单权限数量: {len(created_user.menu_permissions)}")
                
                # 清理测试数据
                print("\n=== 清理测试数据 ===")
                db.session.delete(created_user)
                db.session.commit()
                print("✅ 测试数据清理完成")
            else:
                print("❌ 用户创建验证失败")
            
            print("\n=== 测试完成 ===")
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {str(e)}")
            db.session.rollback()
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_add_user()
