<!DOCTYPE html>
<html>
<head>
    <title>CSRF测试</title>
</head>
<body>
    <h1>CSRF测试</h1>
    
    <form id="testForm">
        <input type="hidden" name="csrf_token" value="test-token">
        <input type="text" name="username" placeholder="用户名" required>
        <input type="email" name="email" placeholder="邮箱" required>
        <input type="password" name="password" placeholder="密码" required>
        <button type="submit">测试提交</button>
    </form>

    <div id="result"></div>

    <script>
    function getCSRFToken() {
        const csrfInput = document.querySelector('input[name="csrf_token"]');
        return csrfInput ? csrfInput.value : '';
    }

    document.getElementById('testForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const data = {
            username: formData.get('username'),
            email: formData.get('email'),
            password: formData.get('password')
        };
        
        console.log('发送数据:', data);
        console.log('CSRF令牌:', getCSRFToken());
        
        fetch('http://127.0.0.1:5000/simple/users/add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRFToken': getCSRFToken()
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            console.log('响应状态:', response.status);
            return response.text();
        })
        .then(text => {
            console.log('响应内容:', text);
            document.getElementById('result').innerHTML = `
                <h3>响应结果:</h3>
                <pre>${text}</pre>
            `;
        })
        .catch(error => {
            console.error('错误:', error);
            document.getElementById('result').innerHTML = `
                <h3>错误:</h3>
                <pre>${error}</pre>
            `;
        });
    });
    </script>
</body>
</html>
