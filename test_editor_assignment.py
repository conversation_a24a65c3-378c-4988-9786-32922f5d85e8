#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试编辑人员分配功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app
    from app.models import db
    from app.models.user import User
    from app.models.content import Content
    print("✅ 模块导入成功")
except Exception as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

def test_editor_assignment():
    """测试编辑人员分配功能"""
    app = create_app()
    
    with app.app_context():
        try:
            print("=== 测试编辑人员分配功能 ===")
            
            # 1. 检查数据库字段是否存在
            print("\n1. 检查数据库字段...")
            
            # 查询一个Content记录来验证字段
            content = Content.query.first()
            if content:
                print(f"✅ 找到Content记录，ID: {content.id}")
                print(f"   image_editor_id: {getattr(content, 'image_editor_id', 'NOT_FOUND')}")
                print(f"   content_editor_id: {getattr(content, 'content_editor_id', 'NOT_FOUND')}")
            else:
                print("⚠️ 没有找到Content记录")
            
            # 2. 检查用户列表
            print("\n2. 检查用户列表...")
            users = User.query.filter_by(is_active=True).all()
            print(f"✅ 找到 {len(users)} 个活跃用户:")
            for user in users:
                print(f"   - ID: {user.id}, 用户名: {user.username}, 真实姓名: {user.real_name or '未设置'}")
            
            # 3. 测试创建带有编辑人员分配的Content
            print("\n3. 测试创建带有编辑人员分配的Content...")
            if len(users) >= 2:
                # 创建一个测试Content
                test_content = Content(
                    title="测试文案标题",
                    content="这是一个测试文案内容",
                    template_id=1,  # 假设存在ID为1的模板
                    created_by=users[0].id,
                    image_editor_id=users[0].id,
                    content_editor_id=users[1].id if len(users) > 1 else users[0].id
                )
                
                db.session.add(test_content)
                db.session.commit()
                
                print(f"✅ 创建测试Content成功，ID: {test_content.id}")
                print(f"   图片编辑管理员: {test_content.image_editor_id} ({users[0].username})")
                print(f"   文案编辑管理员: {test_content.content_editor_id} ({users[1].username if len(users) > 1 else users[0].username})")
                
                # 验证关联关系
                if test_content.image_editor:
                    print(f"   图片编辑管理员关联: {test_content.image_editor.username}")
                if test_content.content_editor:
                    print(f"   文案编辑管理员关联: {test_content.content_editor.username}")
                
                # 清理测试数据
                db.session.delete(test_content)
                db.session.commit()
                print("✅ 测试数据已清理")
            else:
                print("⚠️ 用户数量不足，跳过创建测试")
            
            # 4. 检查现有Content的分配情况
            print("\n4. 检查现有Content的分配情况...")
            contents_with_assignment = Content.query.filter(
                db.or_(
                    Content.image_editor_id.isnot(None),
                    Content.content_editor_id.isnot(None)
                )
            ).limit(5).all()
            
            if contents_with_assignment:
                print(f"✅ 找到 {len(contents_with_assignment)} 个已分配的Content:")
                for content in contents_with_assignment:
                    print(f"   - ID: {content.id}, 标题: {content.title[:20]}...")
                    if content.image_editor_id:
                        print(f"     图片编辑: {content.image_editor.username if content.image_editor else 'Unknown'}")
                    if content.content_editor_id:
                        print(f"     文案编辑: {content.content_editor.username if content.content_editor else 'Unknown'}")
            else:
                print("⚠️ 没有找到已分配的Content")
            
            print("\n=== 测试完成 ===")
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_editor_assignment()
