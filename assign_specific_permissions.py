#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
给用户分配特定权限
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app
    from app.models import db
    from app.models.user import User, Permission
    print("✅ 模块导入成功")
except Exception as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

def assign_specific_permissions():
    """给用户分配特定权限"""
    app = create_app()
    
    with app.app_context():
        try:
            print("=== 给用户分配特定权限 ===")
            
            # 权限分配计划
            permission_assignments = {
                'content.review': ['reviewer', 'final_reviewer'],  # 初审权限
                'image.upload': ['editor']  # 图片上传权限
            }
            
            for permission_name, usernames in permission_assignments.items():
                print(f"\n处理权限: {permission_name}")
                
                # 查找或创建权限
                permission = Permission.query.filter_by(name=permission_name).first()
                if not permission:
                    permission_desc = {
                        'content.review': '内容审核',
                        'image.upload': '图片上传'
                    }.get(permission_name, permission_name)
                    
                    permission = Permission(name=permission_name, description=permission_desc)
                    db.session.add(permission)
                    db.session.flush()
                    print(f"  ✅ 创建权限: {permission_name}")
                else:
                    print(f"  ✅ 找到权限: {permission_name}")
                
                # 给用户分配权限
                for username in usernames:
                    user = User.query.filter_by(username=username).first()
                    if not user:
                        print(f"  ❌ 找不到用户: {username}")
                        continue
                    
                    # 检查用户是否已经有这个权限
                    if permission in user.permissions:
                        print(f"  ✅ 用户 {username} 已经有权限 {permission_name}")
                    else:
                        # 添加权限
                        user.permissions.append(permission)
                        print(f"  ✅ 给用户 {username} 添加权限 {permission_name}")
            
            # 提交更改
            db.session.commit()
            print("\n✅ 权限分配完成！")
            
            # 验证权限分配结果
            print("\n=== 验证权限分配结果 ===")
            
            all_users = User.query.filter_by(is_active=True).all()
            
            for user in all_users:
                print(f"\n用户: {user.username} ({user.real_name or '未设置'})")
                
                # 检查初审权限
                has_review = user.has_permission('content.review')
                print(f"  初审权限 (content.review): {'✅ 有' if has_review else '❌ 没有'}")
                
                # 检查图片上传权限
                has_image = user.has_permission('image.upload')
                print(f"  图片上传权限 (image.upload): {'✅ 有' if has_image else '❌ 没有'}")
            
        except Exception as e:
            print(f"❌ 权限分配过程中出错: {str(e)}")
            db.session.rollback()
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    assign_specific_permissions()
