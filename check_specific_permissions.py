#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查特定权限的用户
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app
    from app.models import db
    from app.models.user import User
    print("✅ 模块导入成功")
except Exception as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

def check_specific_permissions():
    """检查特定权限的用户"""
    app = create_app()
    
    with app.app_context():
        try:
            print("=== 检查特定权限的用户 ===")
            
            # 检查所有活跃用户
            users = User.query.filter_by(is_active=True).all()
            print(f"\n所有活跃用户数量: {len(users)}")
            
            # 需要检查的权限
            permissions_to_check = {
                'content.review': '初审权限',
                'image.upload': '图片上传权限'
            }
            
            print(f"\n=== 权限检查结果 ===")
            
            for permission_name, permission_desc in permissions_to_check.items():
                print(f"\n{permission_desc} ({permission_name}):")
                users_with_permission = []
                
                for user in users:
                    if user.has_permission(permission_name):
                        users_with_permission.append(user)
                        print(f"  ✅ {user.username} ({user.real_name or '未设置'})")
                
                if not users_with_permission:
                    print(f"  ❌ 没有用户拥有 {permission_desc}")
                else:
                    print(f"  总计: {len(users_with_permission)} 个用户")
            
            print(f"\n=== 详细权限分析 ===")
            
            for user in users:
                print(f"\n用户: {user.username} ({user.real_name or '未设置'})")
                print(f"  角色: {[role.name for role in user.roles]}")
                
                # 检查每个权限
                for permission_name, permission_desc in permissions_to_check.items():
                    has_permission = user.has_permission(permission_name)
                    print(f"  {permission_desc}: {'✅ 有' if has_permission else '❌ 没有'}")
            
            print("\n=== 检查完成 ===")
            
        except Exception as e:
            print(f"❌ 检查过程中出错: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    check_specific_permissions()
