#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查用户数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app
    from app.models import db
    from app.models.user import User
    print("✅ 模块导入成功")
except Exception as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

def check_users():
    """检查用户数据"""
    app = create_app()
    
    with app.app_context():
        try:
            print("=== 检查用户数据 ===")
            
            # 检查所有用户
            all_users = User.query.all()
            print(f"\n所有用户数量: {len(all_users)}")
            
            # 检查活跃用户
            active_users = User.query.filter_by(is_active=True).all()
            print(f"活跃用户数量: {len(active_users)}")
            
            print("\n活跃用户详情:")
            for user in active_users:
                display_name = user.real_name or user.username
                print(f"  ID: {user.id:2d} | 用户名: {user.username:15s} | 真实姓名: {user.real_name or '未设置':15s} | 显示名: {display_name}")
            
            # 生成用户选择项
            user_choices = [(0, '-- 不分配 --')] + [(u.id, f"{u.real_name or u.username}") for u in active_users]
            
            print(f"\n用户选择项数量: {len(user_choices)}")
            print("用户选择项详情:")
            for choice_id, choice_name in user_choices:
                print(f"  值: {choice_id:2d} | 显示: {choice_name}")
            
            print("\n=== 检查完成 ===")
            
        except Exception as e:
            print(f"❌ 检查过程中出错: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    check_users()
