#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查用户权限设置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app
    from app.models import db
    from app.models.user import User, Permission, Role
    from app.models.menu import MenuItem
    print("✅ 模块导入成功")
except Exception as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

def check_user_permissions():
    """检查用户权限设置"""
    app = create_app()
    
    with app.app_context():
        try:
            print("=== 检查用户权限设置 ===")
            
            # 获取所有活跃用户
            users = User.query.filter_by(is_active=True).all()
            
            print(f"\n活跃用户数量: {len(users)}")
            
            for user in users:
                print(f"\n用户: {user.username} ({user.real_name or '未设置'})")
                print(f"  用户ID: {user.id}")
                
                # 检查角色
                roles = [role.name for role in user.roles]
                print(f"  角色: {roles}")
                
                # 检查权限
                permissions = [perm.name for perm in user.permissions]
                print(f"  直接权限: {permissions}")
                
                # 检查通过角色获得的权限
                role_permissions = []
                for role in user.roles:
                    role_perms = [perm.name for perm in role.permissions]
                    role_permissions.extend(role_perms)
                
                print(f"  角色权限: {list(set(role_permissions))}")
                
                # 检查特定权限
                has_content_review = user.has_permission('content.review')
                print(f"  是否有content.review权限: {'是' if has_content_review else '否'}")
                
                # 检查菜单权限
                menu_permissions = [menu.name for menu in user.menu_permissions]
                print(f"  菜单权限: {menu_permissions}")
            
            print("\n=== 所有权限列表 ===")
            permissions = Permission.query.all()
            for perm in permissions:
                print(f"权限: {perm.name} - {perm.description}")
            
            print("\n=== 所有菜单列表 ===")
            menus = MenuItem.query.all()
            for menu in menus:
                print(f"菜单: {menu.name} (ID: {menu.id}) - 权限: {menu.permission}")
            
            print("\n=== 检查完成 ===")
            
        except Exception as e:
            print(f"❌ 检查过程中出错: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    check_user_permissions()
