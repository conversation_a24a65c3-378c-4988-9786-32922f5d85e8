#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试文案编辑管理员筛选逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app
    from app.models import db
    from app.models.user import User
    print("✅ 模块导入成功")
except Exception as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

def test_content_editor_filter():
    """测试文案编辑管理员筛选逻辑"""
    app = create_app()
    
    with app.app_context():
        try:
            print("=== 测试文案编辑管理员筛选逻辑 ===")
            
            # 获取所有活跃用户
            active_users = User.query.filter_by(is_active=True).all()
            print(f"\n活跃用户数量: {len(active_users)}")
            
            # 原来的筛选逻辑（基于content.review权限）
            old_content_editors = [u for u in active_users if u.has_permission('content.review')]
            print(f"\n=== 原来的筛选逻辑（基于content.review权限） ===")
            print(f"筛选出的用户数量: {len(old_content_editors)}")
            for user in old_content_editors:
                print(f"  - {user.username} ({user.real_name or '未设置'})")
            
            # 新的筛选逻辑（基于"初审文案"菜单权限）
            def has_content_review_menu(user):
                """检查用户是否有初审文案菜单权限"""
                return any(menu.name == '初审文案' for menu in user.menu_permissions)
            
            new_content_editors = [u for u in active_users if has_content_review_menu(u)]
            print(f"\n=== 新的筛选逻辑（基于'初审文案'菜单权限） ===")
            print(f"筛选出的用户数量: {len(new_content_editors)}")
            for user in new_content_editors:
                print(f"  - {user.username} ({user.real_name or '未设置'})")
            
            # 详细分析每个用户的权限情况
            print(f"\n=== 详细权限分析 ===")
            for user in active_users:
                has_permission = user.has_permission('content.review')
                has_menu = has_content_review_menu(user)
                menu_names = [menu.name for menu in user.menu_permissions]
                
                print(f"\n用户: {user.username} ({user.real_name or '未设置'})")
                print(f"  content.review权限: {'有' if has_permission else '无'}")
                print(f"  初审文案菜单: {'有' if has_menu else '无'}")
                print(f"  所有菜单权限: {menu_names}")
                
                # 标记变化
                if has_permission != has_menu:
                    if has_permission and not has_menu:
                        print(f"  ⚠️  变化: 原来会显示，现在不显示")
                    elif not has_permission and has_menu:
                        print(f"  ✅ 变化: 原来不显示，现在会显示")
            
            # 生成选择项格式
            print(f"\n=== 生成的选择项 ===")
            content_editors_sorted = sorted(new_content_editors, key=lambda u: (u.is_admin, u.id))
            content_editor_choices = [(u.id, f"{u.real_name or u.username}") for u in content_editors_sorted]
            
            print("文案编辑管理员下拉框选项:")
            for choice_id, choice_name in content_editor_choices:
                print(f"  {choice_id}: {choice_name}")
            
            print("\n=== 测试完成 ===")
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_content_editor_filter()
