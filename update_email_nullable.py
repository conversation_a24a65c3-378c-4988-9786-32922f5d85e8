#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
更新email字段为可空
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app
    from app.models import db
    print("✅ 模块导入成功")
except Exception as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

def update_email_nullable():
    """更新email字段为可空"""
    app = create_app()
    
    with app.app_context():
        try:
            print("=== 更新email字段为可空 ===")
            
            # 执行SQL命令更新表结构
            sql_commands = [
                # 先修改字段为可空
                "ALTER TABLE users MODIFY COLUMN email VARCHAR(100) NULL",

                # 然后将现有的空字符串邮箱更新为NULL
                "UPDATE users SET email = NULL WHERE email = ''"
            ]
            
            for sql in sql_commands:
                print(f"执行SQL: {sql}")
                db.session.execute(db.text(sql))
            
            db.session.commit()
            print("✅ 数据库结构更新成功")
            
            # 验证更改
            result = db.session.execute(db.text("DESCRIBE users")).fetchall()
            for row in result:
                if row[0] == 'email':
                    print(f"email字段信息: {row}")
                    break
            
            print("\n=== 更新完成 ===")
            
        except Exception as e:
            print(f"❌ 更新过程中出错: {str(e)}")
            db.session.rollback()
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    update_email_nullable()
