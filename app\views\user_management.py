# -*- coding: utf-8 -*-
"""
用户管理视图
"""

from datetime import datetime
from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from app.models.user import User, Role, Permission
from app.models.menu import MenuItem
from app.models import db
from app.utils.decorators import admin_required, ajax_aware
from sqlalchemy import or_

# 创建用户管理蓝图
user_management_bp = Blueprint('user_management', __name__)

# 定义菜单权限映射
MENU_PERMISSIONS = {
    'content': 'content_generate',  # 内容生成菜单权限映射
    
    'dashboard': 'dashboard.view',
    'templates': 'template.manage',
    'clients': 'client.manage', 
    'content': 'content.generate',
    'review-content': 'content.review',
    'image-upload': 'image.upload',
    'final-review': 'content.final_review',
    'client-review': 'client.review',
    'publish-manage': 'publish.manage',
    'publish-status-manage': 'publish.status',
    'users': 'user.manage',
    'system': 'system.settings'
}

@user_management_bp.route('/simple/users')
@login_required
@admin_required
def user_list():
    """用户列表页面"""
    page = request.args.get('page', 1, type=int)
    per_page = 20
    
    # 搜索参数
    search = request.args.get('search', '')
    role_filter = request.args.get('role', '')
    status_filter = request.args.get('status', '')
    
    # 构建查询
    query = User.query
    
    if search:
        query = query.filter(
            or_(
                User.username.contains(search),
                User.email.contains(search),
                User.real_name.contains(search)
            )
        )
    
    if role_filter:
        query = query.join(User.roles).filter(Role.name == role_filter)
    
    if status_filter:
        if status_filter == 'active':
            query = query.filter(User.is_active == True)
        elif status_filter == 'inactive':
            query = query.filter(User.is_active == False)
    
    # 分页
    pagination = query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # 获取所有角色用于筛选
    roles = Role.query.all()
    
    return render_template('user_management/list.html', 
                         pagination=pagination,
                         roles=roles,
                         search=search,
                         role_filter=role_filter,
                         status_filter=status_filter)

@user_management_bp.route('/simple/users/add', methods=['GET', 'POST'])
@login_required
@admin_required
@ajax_aware
def add_user():
    """添加用户"""
    if request.method == 'POST':
        try:
            data = request.get_json()
            print(f"DEBUG - 接收到的数据: {data}")

            # 验证必需字段
            if not data:
                print("DEBUG - 没有接收到JSON数据")
                return jsonify({'success': False, 'message': '没有接收到数据'}), 400

            required_fields = ['username', 'email', 'password']
            for field in required_fields:
                if field not in data or not data[field]:
                    print(f"DEBUG - 缺少必需字段: {field}")
                    return jsonify({'success': False, 'message': f'缺少必需字段: {field}'}), 400

            # 检查用户名和邮箱是否已存在
            if User.query.filter_by(username=data['username']).first():
                return jsonify({'success': False, 'message': '用户名已存在'})

            if User.query.filter_by(email=data['email']).first():
                return jsonify({'success': False, 'message': '邮箱已存在'})
            
            # 创建新用户
            user = User(
                username=data['username'],
                email=data['email'],
                real_name=data.get('real_name', ''),
                phone=data.get('phone', ''),
                is_active=data.get('is_active', True)
            )
            user.password = data['password']
            
            # 分配菜单权限
            if 'menu_permissions' in data:
                # 处理publish_manage表单字段映射到publish.manage权限
                if data.get('publish_manage', False):
                    publish_menu = MenuItem.query.filter_by(slug='publish-manage').first()
                    if publish_menu and publish_menu.id not in data['menu_permissions']:
                        data['menu_permissions'].append(publish_menu.id)

                # 处理content_generate表单字段映射到content.generate权限
                content_menu = MenuItem.query.filter_by(slug='content').first()
                if content_menu:
                    if data.get('content_generate', False):
                        if content_menu.id not in data['menu_permissions']:
                            data['menu_permissions'].append(content_menu.id)
                    else:
                        if content_menu.id in data['menu_permissions']:
                            data['menu_permissions'].remove(content_menu.id)
            
            # 更新用户权限
            if data.get('content_generate', False):
                # 查找或创建content_generate权限
                from app.models.user import Permission
                perm = Permission.query.filter_by(name='content.generate').first()
                if not perm:
                    perm = Permission(name='content.generate', description='内容生成权限')
                    db.session.add(perm)
                    db.session.flush()

                # 添加权限到用户
                if perm not in user.permissions:
                    user.permissions.append(perm)

            # 分配菜单权限
            if 'menu_permissions' in data and data['menu_permissions']:
                menu_items = MenuItem.query.filter(MenuItem.id.in_(data['menu_permissions'])).all()
                user.menu_permissions = menu_items
            
            db.session.add(user)
            db.session.commit()
            
            return jsonify({'success': True, 'message': '用户添加成功'})
            
        except Exception as e:
            db.session.rollback()
            print(f"DEBUG - 用户添加异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return jsonify({'success': False, 'message': f'添加失败: {str(e)}'}), 500
    
    # GET请求，返回添加用户页面
    menu_items = MenuItem.query.filter_by(is_active=True).order_by(MenuItem.order).all()
    
    return render_template('user_management/add.html',
                         menu_items=menu_items)

@user_management_bp.route('/simple/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
@admin_required
@ajax_aware
def edit_user(user_id):
    """编辑用户"""
    user = User.query.get_or_404(user_id)
    
    if request.method == 'POST':
        try:
            data = request.get_json()
            
            # 检查用户名和邮箱是否已被其他用户使用
            existing_user = User.query.filter_by(username=data['username']).first()
            if existing_user and existing_user.id != user_id:
                return jsonify({'success': False, 'message': '用户名已存在'})
            
            existing_user = User.query.filter_by(email=data['email']).first()
            if existing_user and existing_user.id != user_id:
                return jsonify({'success': False, 'message': '邮箱已存在'})
            
            # 更新用户信息
            user.username = data['username']
            user.email = data['email']
            user.real_name = data.get('real_name', '')
            user.phone = data.get('phone', '')
            user.is_active = data.get('is_active', True)
            
            # 更新密码（如果提供）
            if 'password' in data and data['password']:
                user.password = data['password']
            
            # 更新菜单权限
            if 'menu_permissions' in data:
                # 处理publish_manage表单字段映射到publish.manage权限
                if data.get('publish_manage', False):
                    publish_menu = MenuItem.query.filter_by(slug='publish-manage').first()
                    if publish_menu and publish_menu.id not in data['menu_permissions']:
                        data['menu_permissions'].append(publish_menu.id)
                menu_items = MenuItem.query.filter(MenuItem.id.in_(data['menu_permissions'])).all()
                user.menu_permissions = menu_items
            
            db.session.commit()
            
            return jsonify({'success': True, 'message': '用户更新成功'})
            
        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'message': f'更新失败: {str(e)}'})
    
    # GET请求，返回编辑用户页面
    menu_items = MenuItem.query.filter_by(is_active=True).order_by(MenuItem.order).all()
    
    return render_template('user_management/edit.html',
                         user=user,
                         menu_items=menu_items)

@user_management_bp.route('/simple/users/<int:user_id>/delete', methods=['POST'])
@login_required
@admin_required
@ajax_aware
def delete_user(user_id):
    """删除用户"""
    user = User.query.get_or_404(user_id)
    
    # 不能删除自己
    if user.id == current_user.id:
        return jsonify({'success': False, 'message': '不能删除当前登录用户'})
    
    try:
        # 删除用户的所有关联数据
        user.roles.clear()
        user.permissions.clear()
        db.session.delete(user)
        db.session.commit()
        
        return jsonify({'success': True, 'message': '用户删除成功'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'删除失败: {str(e)}'})

@user_management_bp.route('/simple/users/<int:user_id>/toggle-status', methods=['POST'])
@login_required
@admin_required
@ajax_aware
def toggle_user_status(user_id):
    """切换用户状态"""
    user = User.query.get_or_404(user_id)
    
    # 不能禁用自己
    if user.id == current_user.id:
        return jsonify({'success': False, 'message': '不能修改当前登录用户状态'})
    
    try:
        user.is_active = not user.is_active
        db.session.commit()
        
        status_text = '启用' if user.is_active else '禁用'
        return jsonify({'success': True, 'message': f'用户已{status_text}'})
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'操作失败: {str(e)}'})

@user_management_bp.route('/api/users/search')
@login_required
@admin_required
def search_users():
    """搜索用户API"""
    search = request.args.get('q', '')
    if not search:
        return jsonify([])
    
    users = User.query.filter(
        or_(
            User.username.contains(search),
            User.email.contains(search),
            User.real_name.contains(search)
        )
    ).limit(10).all()
    
    return jsonify([{
        'id': user.id,
        'username': user.username,
        'email': user.email,
        'real_name': user.real_name
    } for user in users])

@user_management_bp.route('/api/roles')
@login_required
@admin_required
def get_roles():
    """获取角色列表API"""
    roles = Role.query.all()
    return jsonify([{
        'id': role.id,
        'name': role.name,
        'description': role.description
    } for role in roles])

@user_management_bp.route('/api/permissions')
@login_required
@admin_required
def get_permissions():
    """获取所有权限"""
    permissions = Permission.query.all()
    return jsonify({
        'success': True,
        'permissions': [{'id': p.id, 'name': p.name, 'description': p.description} for p in permissions]
    })

@user_management_bp.route('/simple/users/<int:user_id>/permissions', methods=['GET'])
@login_required
@admin_required
def get_user_permissions(user_id):
    """获取用户权限设置"""
    user = User.query.get_or_404(user_id)
    
    # 获取所有菜单项
    menu_items = MenuItem.query.filter_by(is_active=True).order_by(MenuItem.order).all()
    
    # 获取用户当前的菜单权限
    user_menu_permissions = [menu.id for menu in user.get_menu_items()]
    
    return jsonify({
        'success': True,
        'menu_items': [{
            'id': menu.id,
            'name': menu.name,
            'icon': menu.icon,
            'permission': menu.permission
        } for menu in menu_items],
        'user_permissions': user_menu_permissions
    })

@user_management_bp.route('/simple/users/<int:user_id>/permissions', methods=['POST'])
@login_required
@admin_required
@ajax_aware
def update_user_permissions(user_id):
    """更新用户权限设置"""
    user = User.query.get_or_404(user_id)
    data = request.get_json()
    
    try:
        # 更新用户的菜单权限
        menu_permissions = data.get('menu_permissions', [])
        
        # 清除用户现有的菜单权限
        user.menu_permissions = []
        
        # 添加新的菜单权限
        for menu_id in menu_permissions:
            menu = MenuItem.query.get(menu_id)
            if menu:
                user.menu_permissions.append(menu)
        
        db.session.commit()
        
        return jsonify({
            'success': True,
            'message': f'用户 {user.username} 的权限设置已更新'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'message': f'更新权限失败: {str(e)}'
        })