#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试表单渲染
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models import db
from app.models.user import User
from app.models.client import Client
from app.models.template import TemplateCategory
from app.forms.content import GenerateContentForm

def debug_form():
    """调试表单渲染"""
    app = create_app()
    
    with app.app_context():
        with app.test_request_context():
            try:
                print("=== 调试表单渲染 ===")
                
                # 创建表单
                form = GenerateContentForm()
                
                # 初始化选择项
                print("\n1. 初始化选择项...")
                
                # 客户选择项
                clients = Client.query.filter_by(status=True).all()
                form.client_id.choices = [(c.id, c.name) for c in clients]
                print(f"✅ 客户选择项: {len(form.client_id.choices)} 个")
                
                # 模板分类选择项
                categories = TemplateCategory.query.all()
                form.template_category_id.choices = [(c.id, c.name) for c in categories]
                print(f"✅ 模板分类选择项: {len(form.template_category_id.choices)} 个")
                
                # 用户选择项
                active_users = User.query.filter_by(is_active=True).all()
                user_choices = [(0, '-- 不分配 --')] + [(u.id, f"{u.real_name or u.username}") for u in active_users]
                form.image_editor_id.choices = user_choices
                form.content_editor_id.choices = user_choices
                print(f"✅ 用户选择项: {len(user_choices)} 个")
                
                # 测试字段渲染
                print("\n2. 测试字段渲染...")
                
                # 渲染图片编辑选择框
                image_editor_html = str(form.image_editor_id)
                print(f"✅ 图片编辑选择框渲染成功，HTML长度: {len(image_editor_html)}")
                
                # 渲染文案编辑选择框
                content_editor_html = str(form.content_editor_id)
                print(f"✅ 文案编辑选择框渲染成功，HTML长度: {len(content_editor_html)}")
                
                # 检查HTML内容
                print("\n3. 检查HTML内容...")
                
                print("图片编辑选择框HTML:")
                print(image_editor_html[:500] + "..." if len(image_editor_html) > 500 else image_editor_html)
                
                print("\n文案编辑选择框HTML:")
                print(content_editor_html[:500] + "..." if len(content_editor_html) > 500 else content_editor_html)
                
                # 检查是否包含用户选项
                print("\n4. 检查用户选项...")
                for user in active_users[:3]:  # 检查前3个用户
                    display_name = user.real_name or user.username
                    if display_name in image_editor_html:
                        print(f"✅ 图片编辑选择框包含用户: {display_name}")
                    else:
                        print(f"❌ 图片编辑选择框缺少用户: {display_name}")
                        
                    if display_name in content_editor_html:
                        print(f"✅ 文案编辑选择框包含用户: {display_name}")
                    else:
                        print(f"❌ 文案编辑选择框缺少用户: {display_name}")
                
                print("\n=== 调试完成 ===")
                
            except Exception as e:
                print(f"❌ 调试过程中出错: {str(e)}")
                import traceback
                traceback.print_exc()

if __name__ == '__main__':
    debug_form()
