# 用户分配功能说明

## 功能概述

在内容生成页面添加了用户分配功能，可以为每篇生成的文案分配专门的编辑人员，实现更精细化的工作流管理。

## 主要功能

### 1. 内容生成时的用户分配

在内容生成页面（`http://127.0.0.1:5000/simple/content`）新增了"编辑人员分配"区域，包含两个下拉框：

- **文案编辑管理员（初审）**：负责初审文案内容的管理员
- **图片编辑管理员**：负责图片编辑的管理员

### 2. 用户选择选项

- 下拉框显示所有活跃用户（`is_active=True`）
- 显示格式：真实姓名（如果有）或用户名
- 包含"-- 不分配 --"选项，允许不分配特定用户

### 3. 初审页面的用户过滤

在初审文案页面，系统会自动根据当前登录用户进行过滤：

- **超级管理员**：可以看到所有文案
- **普通用户**：如果有分配给该用户的文案，则只显示分配给该用户的文案
- **未分配用户**：如果没有任何文案分配给该用户，则显示所有文案

## 数据库变更

### 新增字段

在 `contents` 表中添加了两个新字段：

```sql
-- 图片编辑管理员ID
image_editor_id INT NULL COMMENT '图片编辑管理员ID',

-- 文案编辑管理员ID（初审）
content_editor_id INT NULL COMMENT '文案编辑管理员ID（初审）',

-- 外键约束
FOREIGN KEY (image_editor_id) REFERENCES users(id) ON DELETE SET NULL,
FOREIGN KEY (content_editor_id) REFERENCES users(id) ON DELETE SET NULL
```

### 索引优化

为提高查询性能，添加了相应的索引：

```sql
CREATE INDEX idx_contents_image_editor_id ON contents(image_editor_id);
CREATE INDEX idx_contents_content_editor_id ON contents(content_editor_id);
```

## 代码变更

### 1. 模型更新（app/models/content.py）

```python
# 编辑人员分配字段
image_editor_id = db.Column(db.Integer, db.ForeignKey('users.id'))
content_editor_id = db.Column(db.Integer, db.ForeignKey('users.id'))

# 关联关系
image_editor = db.relationship('User', foreign_keys=[image_editor_id], ...)
content_editor = db.relationship('User', foreign_keys=[content_editor_id], ...)
```

### 2. 表单更新（app/forms/content.py）

```python
# 新增：编辑人员分配字段
image_editor_id = SelectField('图片编辑管理员', coerce=int, validators=[Optional()])
content_editor_id = SelectField('文案编辑管理员（初审）', coerce=int, validators=[Optional()])
```

### 3. 视图函数更新

- **内容生成视图**：初始化用户选择项，处理分配参数
- **初审页面视图**：添加用户过滤逻辑
- **内容生成服务**：支持编辑人员分配参数

### 4. 模板更新

在内容生成页面模板中添加了用户分配区域的HTML代码。

## 使用流程

### 1. 生成文案时分配用户

1. 进入内容生成页面
2. 填写基本信息（客户、任务、模板等）
3. 在"编辑人员分配"区域选择对应的管理员
4. 生成文案，系统会自动将分配信息保存到数据库

### 2. 初审时的用户过滤

1. 用户登录系统
2. 进入初审文案页面
3. 系统自动根据用户权限和分配情况显示相应的文案
4. 用户只能看到分配给自己的文案（超级管理员除外）

## 权限控制

- **超级管理员**：可以看到所有文案，不受分配限制
- **分配用户**：只能看到分配给自己的文案
- **未分配用户**：如果没有任何分配，可以看到所有文案

## 技术特点

1. **向后兼容**：新字段允许为空，不影响现有数据
2. **性能优化**：添加了索引，提高查询效率
3. **灵活分配**：支持不分配特定用户的情况
4. **权限控制**：根据用户角色和分配情况自动过滤

## 测试验证

通过测试脚本验证了以下功能：

- ✅ 数据库字段正确添加
- ✅ 用户列表正常获取
- ✅ 字段可以正常赋值和查询
- ✅ 外键关系正常工作

## 后续扩展

该功能为后续的工作流优化奠定了基础，可以进一步扩展：

1. **通知系统**：当文案分配给用户时发送通知
2. **工作量统计**：统计每个用户处理的文案数量
3. **权限细化**：更精细的权限控制
4. **批量分配**：支持批量分配功能

## 注意事项

1. 分配信息在文案生成时设置，后续可以通过编辑功能修改
2. 删除用户时，相关分配会自动设置为NULL（ON DELETE SET NULL）
3. 超级管理员始终可以看到所有文案，不受分配限制
4. 如果用户没有任何分配的文案，系统会显示所有文案以保证可用性
