#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试权限过滤功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app
    from app.models import db
    from app.models.user import User
    print("✅ 模块导入成功")
except Exception as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

def test_permission_filtering():
    """测试权限过滤功能"""
    app = create_app()
    
    with app.app_context():
        try:
            print("=== 测试权限过滤功能 ===")
            
            # 获取所有活跃用户
            active_users = User.query.filter_by(is_active=True).all()
            print(f"\n所有活跃用户数量: {len(active_users)}")
            
            # 过滤有初审权限的用户
            content_editors = [u for u in active_users if u.has_permission('content.review')]
            print(f"\n有初审权限的用户数量: {len(content_editors)}")
            for user in content_editors:
                print(f"  ✅ {user.username} ({user.real_name or '未设置'})")
            
            # 过滤有图片上传权限的用户
            image_editors = [u for u in active_users if u.has_permission('image.upload')]
            print(f"\n有图片上传权限的用户数量: {len(image_editors)}")
            for user in image_editors:
                print(f"  ✅ {user.username} ({user.real_name or '未设置'})")
            
            # 生成选择项
            content_editor_choices = [(0, '-- 不分配 --')] + [(u.id, f"{u.real_name or u.username}") for u in content_editors]
            image_editor_choices = [(0, '-- 不分配 --')] + [(u.id, f"{u.real_name or u.username}") for u in image_editors]
            
            print(f"\n文案编辑管理员选择项:")
            for choice_id, choice_name in content_editor_choices:
                print(f"  ID: {choice_id}, 名称: {choice_name}")
            
            print(f"\n图片编辑管理员选择项:")
            for choice_id, choice_name in image_editor_choices:
                print(f"  ID: {choice_id}, 名称: {choice_name}")
            
            print("\n=== 测试完成 ===")
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_permission_filtering()
