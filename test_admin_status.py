#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试管理员状态
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app
    from app.models import db
    from app.models.user import User
    print("✅ 模块导入成功")
except Exception as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

def test_admin_status():
    """测试管理员状态"""
    app = create_app()
    
    with app.app_context():
        try:
            print("=== 测试管理员状态 ===")
            
            # 获取admin用户
            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                print("❌ 找不到admin用户")
                return
            
            print(f"用户: {admin_user.username}")
            print(f"用户ID: {admin_user.id}")
            print(f"is_admin属性: {admin_user.is_admin}")
            print(f"has_role('超级管理员'): {admin_user.has_role('超级管理员')}")
            
            # 检查用户的角色
            roles = [role.name for role in admin_user.roles]
            print(f"用户角色: {roles}")
            
            # 检查数据库中的is_admin字段
            print(f"数据库is_admin字段: {admin_user.is_admin}")
            
            # 如果is_admin为False，但有超级管理员角色，则更新is_admin字段
            if not admin_user.is_admin and admin_user.has_role('超级管理员'):
                print("⚠️  发现不一致：用户有超级管理员角色但is_admin为False")
                print("正在修复...")
                admin_user.is_admin = True
                db.session.commit()
                print("✅ 已修复is_admin字段")
            elif admin_user.is_admin and not admin_user.has_role('超级管理员'):
                print("⚠️  发现不一致：用户is_admin为True但没有超级管理员角色")
            else:
                print("✅ is_admin字段与角色一致")
            
            print("\n=== 测试完成 ===")
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_admin_status()
