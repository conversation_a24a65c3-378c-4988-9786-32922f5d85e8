#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试CSRF令牌
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app
    from flask import url_for
    import requests
    print("✅ 模块导入成功")
except Exception as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

def test_csrf_token():
    """测试CSRF令牌"""
    app = create_app()
    
    with app.test_client() as client:
        try:
            print("=== 测试CSRF令牌 ===")
            
            # 首先登录
            login_response = client.post('/auth/login', data={
                'username': 'admin',
                'password': 'admin123'
            }, follow_redirects=True)
            
            print(f"登录状态码: {login_response.status_code}")
            
            # 获取添加用户页面
            get_response = client.get('/simple/users/add')
            print(f"GET请求状态码: {get_response.status_code}")
            
            if get_response.status_code == 200:
                # 从响应中提取CSRF令牌
                html_content = get_response.data.decode('utf-8')
                
                # 查找CSRF令牌
                import re
                csrf_match = re.search(r'name="csrf_token" value="([^"]+)"', html_content)
                if csrf_match:
                    csrf_token = csrf_match.group(1)
                    print(f"✅ 找到CSRF令牌: {csrf_token[:20]}...")
                    
                    # 测试POST请求
                    test_data = {
                        'username': 'test_user_csrf',
                        'email': '<EMAIL>',
                        'password': 'test123456',
                        'real_name': '测试用户',
                        'phone': '13800138000',
                        'is_active': True,
                        'menu_permissions': []
                    }
                    
                    # 测试不带CSRF令牌的请求
                    print("\n--- 测试不带CSRF令牌的请求 ---")
                    post_response_no_csrf = client.post('/simple/users/add',
                                                       json=test_data,
                                                       headers={'Content-Type': 'application/json',
                                                               'X-Requested-With': 'XMLHttpRequest'})
                    print(f"不带CSRF令牌的状态码: {post_response_no_csrf.status_code}")
                    
                    # 测试带CSRF令牌的请求
                    print("\n--- 测试带CSRF令牌的请求 ---")
                    post_response_with_csrf = client.post('/simple/users/add',
                                                         json=test_data,
                                                         headers={'Content-Type': 'application/json',
                                                                 'X-Requested-With': 'XMLHttpRequest',
                                                                 'X-CSRFToken': csrf_token})
                    print(f"带CSRF令牌的状态码: {post_response_with_csrf.status_code}")
                    
                    if post_response_with_csrf.status_code == 200:
                        response_data = post_response_with_csrf.get_json()
                        print(f"响应数据: {response_data}")
                    else:
                        print(f"响应内容: {post_response_with_csrf.data.decode('utf-8')[:200]}...")
                    
                else:
                    print("❌ 未找到CSRF令牌")
            else:
                print(f"❌ 无法获取添加用户页面: {get_response.status_code}")
            
            print("\n=== 测试完成 ===")
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_csrf_token()
