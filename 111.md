2025-07-30 17:08:03,830 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-30 17:08:03,830 INFO sqlalchemy.engine.Engine SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.email AS users_email, users.real_name AS users_real_name, users.phone AS users_phone, users.is_active AS users_is_active, users.created_at AS users_created_at, users.last_login AS users_last_login
FROM users
WHERE users.id = %(pk_1)s
2025-07-30 17:08:03,830 INFO sqlalchemy.engine.Engine [cached since 176s ago] {'pk_1': 1}
DEBUG - Loading user admin with ID 1
2025-07-30 17:08:03,831 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:08:03,832 INFO sqlalchemy.engine.Engine [cached since 176s ago] {'param_1': 1}       
DEBUG - User roles: ['超级管理员']
2025-07-30 17:08:03,832 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id   
2025-07-30 17:08:03,833 INFO sqlalchemy.engine.Engine [cached since 176s ago] {'param_1': 2}       
DEBUG - Role 超级管理员 permissions: ['dashboard.view', 'content.final_review', 'publish.manage', 'image.upload', 'template.manage', 'system.settings', 'content.review', 'publish.status', 'client.manage', 'user.manage', 'content.generate', 'client.review']
2025-07-30 17:08:03,834 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items
WHERE menu_items.is_active = true ORDER BY menu_items.`order`
2025-07-30 17:08:03,834 INFO sqlalchemy.engine.Engine [cached since 175.9s ago] {}
2025-07-30 17:08:03,835 INFO sqlalchemy.engine.Engine SELECT clients.id AS clients_id, clients.name AS clients_name, clients.contact AS clients_contact, clients.phone AS clients_phone, clients.email AS clients_email, clients.need_review AS clients_need_review, clients.daily_content_count AS clients_daily_content_count, clients.display_start_time AS clients_display_start_time, clients.interval_min AS clients_interval_min, clients.interval_max AS clients_interval_max, clients.review_timeout_hours AS clients_review_timeout_hours, clients.review_deadline_time AS clients_review_deadline_time, clients.auto_approve_enabled AS clients_auto_approve_enabled, clients.created_at AS clients_created_at, clients.updated_at AS clients_updated_at, clients.status AS clients_status, clients.ext_json 
AS clients_ext_json, clients.default_required_topics AS clients_default_required_topics, clients.default_random_topics AS clients_default_random_topics, clients.default_at_users AS clients_default_at_users, clients.default_location AS clients_default_location
FROM clients
WHERE clients.status = true
2025-07-30 17:08:03,836 INFO sqlalchemy.engine.Engine [cached since 168.8s ago] {}
2025-07-30 17:08:03,837 INFO sqlalchemy.engine.Engine SELECT template_categories.id AS template_categories_id, template_categories.name AS template_categories_name, template_categories.parent_id AS 
template_categories_parent_id, template_categories.sort_order AS template_categories_sort_order, template_categories.created_at AS template_categories_created_at, template_categories.updated_at AS template_categories_updated_at
FROM template_categories
2025-07-30 17:08:03,837 INFO sqlalchemy.engine.Engine [cached since 168.8s ago] {}
2025-07-30 17:08:03,839 INFO sqlalchemy.engine.Engine SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.email AS users_email, users.real_name AS users_real_name, users.phone AS users_phone, users.is_active AS users_is_active, users.created_at AS users_created_at, users.last_login AS users_last_login
FROM users
WHERE users.is_active = true
2025-07-30 17:08:03,839 INFO sqlalchemy.engine.Engine [cached since 168.8s ago] {}
2025-07-30 17:08:03,840 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id2025-07-30 17:08:03,843 INFO sqlalchemy.engine.Engine [cached since 173.4s ago] {'param_1': 1}
2025-07-30 17:08:03,845 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id2025-07-30 17:08:03,846 INFO sqlalchemy.engine.Engine [cached since 173.4s ago] {'param_1': 2}     
2025-07-30 17:08:03,848 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id2025-07-30 17:08:03,848 INFO sqlalchemy.engine.Engine [cached since 173.4s ago] {'param_1': 3}     
2025-07-30 17:08:03,849 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id2025-07-30 17:08:03,850 INFO sqlalchemy.engine.Engine [cached since 173.4s ago] {'param_1': 4}     
2025-07-30 17:08:03,851 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id2025-07-30 17:08:03,851 INFO sqlalchemy.engine.Engine [cached since 173.4s ago] {'param_1': 5}     
2025-07-30 17:08:03,852 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id2025-07-30 17:08:03,852 INFO sqlalchemy.engine.Engine [cached since 173.4s ago] {'param_1': 6}     
2025-07-30 17:08:03,853 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id2025-07-30 17:08:03,853 INFO sqlalchemy.engine.Engine [cached since 173.4s ago] {'param_1': 7}     
2025-07-30 17:08:03,855 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id2025-07-30 17:08:03,856 INFO sqlalchemy.engine.Engine [cached since 173.4s ago] {'param_1': 13}    
2025-07-30 17:08:03,858 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:08:03,858 INFO sqlalchemy.engine.Engine [cached since 176s ago] {'param_1': 2}       
2025-07-30 17:08:03,860 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:08:03,860 INFO sqlalchemy.engine.Engine [cached since 176s ago] {'param_1': 7}       
2025-07-30 17:08:03,861 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:08:03,861 INFO sqlalchemy.engine.Engine [cached since 176s ago] {'param_1': 13}      
2025-07-30 17:08:03,862 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, user_permissions
WHERE %(param_1)s = user_permissions.user_id AND permissions.id = user_permissions.permission_id   
2025-07-30 17:08:03,862 INFO sqlalchemy.engine.Engine [cached since 168.8s ago] {'param_1': 2}     
2025-07-30 17:08:03,862 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id   
2025-07-30 17:08:03,862 INFO sqlalchemy.engine.Engine [cached since 176s ago] {'param_1': 3}       
2025-07-30 17:08:03,863 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:08:03,863 INFO sqlalchemy.engine.Engine [cached since 176s ago] {'param_1': 3}       
2025-07-30 17:08:03,864 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, user_permissions
WHERE %(param_1)s = user_permissions.user_id AND permissions.id = user_permissions.permission_id   
2025-07-30 17:08:03,864 INFO sqlalchemy.engine.Engine [cached since 168.8s ago] {'param_1': 3}     
2025-07-30 17:08:03,865 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:08:03,865 INFO sqlalchemy.engine.Engine [cached since 176s ago] {'param_1': 4}       
2025-07-30 17:08:03,865 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, user_permissions
WHERE %(param_1)s = user_permissions.user_id AND permissions.id = user_permissions.permission_id   
2025-07-30 17:08:03,866 INFO sqlalchemy.engine.Engine [cached since 168.8s ago] {'param_1': 4}     
2025-07-30 17:08:03,866 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id   
2025-07-30 17:08:03,867 INFO sqlalchemy.engine.Engine [cached since 176s ago] {'param_1': 8}       
2025-07-30 17:08:03,867 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:08:03,867 INFO sqlalchemy.engine.Engine [cached since 176s ago] {'param_1': 5}       
2025-07-30 17:08:03,868 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, user_permissions
WHERE %(param_1)s = user_permissions.user_id AND permissions.id = user_permissions.permission_id   
2025-07-30 17:08:03,868 INFO sqlalchemy.engine.Engine [cached since 168.8s ago] {'param_1': 5}     
2025-07-30 17:08:03,869 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id   
2025-07-30 17:08:03,869 INFO sqlalchemy.engine.Engine [cached since 176s ago] {'param_1': 6}       
2025-07-30 17:08:03,870 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:08:03,870 INFO sqlalchemy.engine.Engine [cached since 176s ago] {'param_1': 6}       
2025-07-30 17:08:03,871 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, user_permissions
WHERE %(param_1)s = user_permissions.user_id AND permissions.id = user_permissions.permission_id   
2025-07-30 17:08:03,871 INFO sqlalchemy.engine.Engine [cached since 168.8s ago] {'param_1': 6}     
2025-07-30 17:08:03,872 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id   
2025-07-30 17:08:03,872 INFO sqlalchemy.engine.Engine [cached since 176s ago] {'param_1': 5}       
2025-07-30 17:08:03,875 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, user_permissions
WHERE %(param_1)s = user_permissions.user_id AND permissions.id = user_permissions.permission_id   
2025-07-30 17:08:03,875 INFO sqlalchemy.engine.Engine [cached since 168.8s ago] {'param_1': 7}     
2025-07-30 17:08:03,876 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, user_permissions
WHERE %(param_1)s = user_permissions.user_id AND permissions.id = user_permissions.permission_id   
2025-07-30 17:08:03,877 INFO sqlalchemy.engine.Engine [cached since 168.8s ago] {'param_1': 13}    
2025-07-30 17:08:03,878 INFO sqlalchemy.engine.Engine SELECT tasks.id AS tasks_id, tasks.client_id 
AS tasks_client_id, tasks.name AS tasks_name, tasks.description AS tasks_description, tasks.status 
AS tasks_status, tasks.target_count AS tasks_target_count, tasks.actual_count AS tasks_actual_count, tasks.created_at AS tasks_created_at, tasks.updated_at AS tasks_updated_at, tasks.created_by AS tasks_created_by
FROM tasks
WHERE tasks.id = %(pk_1)s
2025-07-30 17:08:03,878 INFO sqlalchemy.engine.Engine [generated in 0.00033s] {'pk_1': 9}
2025-07-30 17:08:03,879 INFO sqlalchemy.engine.Engine SELECT tasks.id AS tasks_id, tasks.client_id 
AS tasks_client_id, tasks.name AS tasks_name, tasks.description AS tasks_description, tasks.status 
AS tasks_status, tasks.target_count AS tasks_target_count, tasks.actual_count AS tasks_actual_count, tasks.created_at AS tasks_created_at, tasks.updated_at AS tasks_updated_at, tasks.created_by AS tasks_created_by
FROM tasks
WHERE tasks.client_id = %(client_id_1)s
2025-07-30 17:08:03,880 INFO sqlalchemy.engine.Engine [cached since 168.8s ago] {'client_id_1': 1} 
2025-07-30 17:08:03,881 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1
FROM (SELECT batches.id AS batches_id, batches.task_id AS batches_task_id, batches.name AS batches_name, batches.content_count AS batches_content_count, batches.created_at AS batches_created_at, batches.created_by AS batches_created_by
FROM batches
WHERE batches.task_id = %(task_id_1)s) AS anon_1
2025-07-30 17:08:03,881 INFO sqlalchemy.engine.Engine [cached since 123.8s ago] {'task_id_1': 9}   
=== 表单验证通过，开始处理文案生成 ===
表单数据: ImmutableMultiDict([('csrf_token', 'IjM2OTNiM2NhZjI0ZWUwNjMyYWFiMTQ1OTljNjdlMGYxYWY0MWY4OWYi.aIngpw.D9VMD4_JzmX2AWpIJk-u45up1H4'), ('form_validated', '1'), ('client_id', '1'), ('task_id', 
'9'), ('new_task_name', '啊啊啊啊啊啊啊'), ('batch_name', '批次 2'), ('template_category_id', '11'), ('required_topics', 'ee \r\nrr \r\ntt \r\nyy \r\njj \r\nll \r\nbb '), ('random_topics', 'ee \r\nrr \r\ntt \r\nyy \r\njj \r\nll \r\nbb '), ('at_users', '@ee \r\n@rr \r\n@tt \r\n@yy \r\n@jj \r\n@ll 
\r\n@bb '), ('location', 'ee rr tt yy jj ll bb '), ('content_editor_id', '13'), ('image_editor_id', '3'), ('max_topics_count', '10'), ('random_at_users_count', '1'), ('publish_priority', 'normal'), 
('count', '2'), ('duplicate_control', 'task'), ('keywords', '品牌名称: ee, rr, tt, yy\r\n商品名称: 
ee, rr, tt, yy\r\n店铺地址: ee, rr, tt, yy'), ('allow_template_duplicate', '0')])
表单错误: {}
DEBUG - 获取到的表单数据:
  client_id: 1
  task_id: 9
  template_category_id: 11
  count: 2
2025-07-30 17:08:03,882 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1
FROM (SELECT batches.id AS batches_id, batches.task_id AS batches_task_id, batches.name AS batches_name, batches.content_count AS batches_content_count, batches.created_at AS batches_created_at, batches.created_by AS batches_created_by
FROM batches
WHERE batches.task_id = %(task_id_1)s) AS anon_1
2025-07-30 17:08:03,882 INFO sqlalchemy.engine.Engine [cached since 123.8s ago] {'task_id_1': 9}   
2025-07-30 17:08:03,883 INFO sqlalchemy.engine.Engine INSERT INTO batches (task_id, name, content_count, created_at, created_by) VALUES (%(task_id)s, %(name)s, %(content_count)s, %(created_at)s, %(created_by)s)
2025-07-30 17:08:03,883 INFO sqlalchemy.engine.Engine [cached since 78.87s ago] {'task_id': 9, 'name': '批次2', 'content_count': 0, 'created_at': datetime.datetime(2025, 7, 30, 17, 8, 3, 883047), 'created_by': 1}
创建批次: 批次2 (ID: 16) for 任务ID: 9
DEBUG - 接收到的关键词数据: 品牌名称: ee, rr, tt, yy
商品名称: ee, rr, tt, yy
店铺地址: ee, rr, tt, yy
DEBUG - 解析标记 品牌名称: ['ee', 'rr', 'tt', 'yy']
DEBUG - 解析标记 商品名称: ['ee', 'rr', 'tt', 'yy']
DEBUG - 解析标记 店铺地址: ['ee', 'rr', 'tt', 'yy']
DEBUG - 最终解析的关键词字典: {'品牌名称': ['ee', 'rr', 'tt', 'yy'], '商品名称': ['ee', 'rr', 'tt', 'yy'], '店铺地址': ['ee', 'rr', 'tt', 'yy']}
DEBUG - 处理后的数据:
  required_topics: ee
rr
tt
yy
jj
ll
bb
  random_topics: ee
rr
tt
yy
jj
ll
bb
  at_users: @ee
@rr
@tt
@yy
@jj
@ll
@bb
  location: ee rr tt yy jj ll bb
DEBUG - 编辑人员分配: image_editor_id=3, content_editor_id=2
DEBUG - 开始调用generate_contents函数
2025-07-30 17:08:03,885 INFO sqlalchemy.engine.Engine SELECT templates.id AS templates_id, templates.category_id AS templates_category_id, templates.title AS templates_title, templates.content AS templates_content, templates.marks AS templates_marks, templates.creator_id AS templates_creator_id, 
templates.created_at AS templates_created_at, templates.updated_at AS templates_updated_at, templates.status AS templates_status
FROM templates
WHERE templates.category_id = %(category_id_1)s AND templates.status = true
2025-07-30 17:08:03,886 INFO sqlalchemy.engine.Engine [cached since 123.7s ago] {'category_id_1': 11}
2025-07-30 17:08:03,887 INFO sqlalchemy.engine.Engine SELECT DISTINCT contents.template_id AS contents_template_id
FROM contents INNER JOIN templates ON contents.template_id = templates.id
WHERE contents.task_id = %(task_id_1)s AND templates.category_id = %(category_id_1)s AND contents.is_deleted != true
2025-07-30 17:08:03,887 INFO sqlalchemy.engine.Engine [cached since 78.87s ago] {'task_id_1': 9, 'category_id_1': 11}
位置信息调试:
原始location: 'ee rr tt yy jj ll bb'
处理后location_list: ['ee', 'rr', 'tt', 'yy', 'jj', 'll', 'bb']
2025-07-30 17:08:03,888 INFO sqlalchemy.engine.Engine SELECT clients.id AS clients_id, clients.name AS clients_name, clients.contact AS clients_contact, clients.phone AS clients_phone, clients.email AS clients_email, clients.need_review AS clients_need_review, clients.daily_content_count AS clients_daily_content_count, clients.display_start_time AS clients_display_start_time, clients.interval_min AS clients_interval_min, clients.interval_max AS clients_interval_max, clients.review_timeout_hours AS clients_review_timeout_hours, clients.review_deadline_time AS clients_review_deadline_time, clients.auto_approve_enabled AS clients_auto_approve_enabled, clients.created_at AS clients_created_at, clients.updated_at AS clients_updated_at, clients.status AS clients_status, clients.ext_json 
AS clients_ext_json, clients.default_required_topics AS clients_default_required_topics, clients.default_random_topics AS clients_default_random_topics, clients.default_at_users AS clients_default_at_users, clients.default_location AS clients_default_location
FROM clients
WHERE clients.id = %(pk_1)s
2025-07-30 17:08:03,888 INFO sqlalchemy.engine.Engine [cached since 123.9s ago] {'pk_1': 1}        
process_location_for_single_article 输入: ['ee', 'rr', 'tt', 'yy', 'jj', 'll', 'bb']
随机选择的位置: 'bb'
内容长度符合要求 - 标题: 14/20, 内容: 91/1000, 状态设置为: draft, 内部状态: pending
process_location_for_single_article 输入: ['ee', 'rr', 'tt', 'yy', 'jj', 'll', 'bb']
随机选择的位置: 'rr'
内容长度符合要求 - 标题: 17/20, 内容: 91/1000, 状态设置为: draft, 内部状态: pending
2025-07-30 17:08:03,891 INFO sqlalchemy.engine.Engine INSERT INTO contents (client_id, task_id, batch_id, template_id, title, content, topics, location, image_urls, display_date, display_time, workflow_status, publish_status, client_review_status, internal_review_status, publish_priority, publish_time, publish_error, publish_retry_count, status_update_time, created_at, updated_at, created_by, 
reviewer_id, review_time, image_editor_id, content_editor_id, is_deleted, deleted_at, deleted_by, ext_json, content_completed, image_completed) VALUES (%(client_id)s, %(task_id)s, %(batch_id)s, %(template_id)s, %(title)s, %(content)s, %(topics)s, %(location)s, %(image_urls)s, %(display_date)s, %(display_time)s, %(workflow_status)s, %(publish_status)s, %(client_review_status)s, %(internal_review_status)s, %(publish_priority)s, %(publish_time)s, %(publish_error)s, %(publish_retry_count)s, %(status_update_time)s, %(created_at)s, %(updated_at)s, %(created_by)s, %(reviewer_id)s, %(review_time)s, %(image_editor_id)s, %(content_editor_id)s, %(is_deleted)s, %(deleted_at)s, %(deleted_by)s, %(ext_json)s, %(content_completed)s, %(image_completed)s)
2025-07-30 17:08:03,891 INFO sqlalchemy.engine.Engine [cached since 78.87s ago] {'client_id': 1, 'task_id': 9, 'batch_id': 16, 'template_id': 102, 'title': '节日限定✅ yyrr错过等一年！', 'content': '
✅ rr✅ 去\r\n圣诞季必吃的限定款rr！节日氛围拉满，味道更是惊喜～建议提前预约哦 #季节限定 #节日美食', 
'topics': '["ee", "rr", "tt", "yy", "jj", "ll", "bb", "ll", "ee", "tt"]', 'location': 'bb', 'image_urls': None, 'display_date': datetime.date(2025, 7, 30), 'display_time': datetime.time(8, 30), 'workflow_status': 'draft', 'publish_status': 'unpublished', 'client_review_status': 'pending', 'internal_review_status': 'pending', 'publish_priority': 'normal', 'publish_time': None, 'publish_error': 
None, 'publish_retry_count': 0, 'status_update_time': datetime.datetime(2025, 7, 30, 17, 8, 3, 890048), 'created_at': datetime.datetime(2025, 7, 30, 17, 8, 3, 890048), 'updated_at': datetime.datetime(2025, 7, 30, 17, 8, 3, 890048), 'created_by': 1, 'reviewer_id': None, 'review_time': None, 'image_editor_id': 3, 'content_editor_id': 2, 'is_deleted': 0, 'deleted_at': None, 'deleted_by': None, 'ext_json': '{"at_users": ["@ee"]}', 'content_completed': 1, 'image_completed': 1}
2025-07-30 17:08:03,892 INFO sqlalchemy.engine.Engine INSERT INTO contents (client_id, task_id, batch_id, template_id, title, content, topics, location, image_urls, display_date, display_time, workflow_status, publish_status, client_review_status, internal_review_status, publish_priority, publish_time, publish_error, publish_retry_count, status_update_time, created_at, updated_at, created_by, 
reviewer_id, review_time, image_editor_id, content_editor_id, is_deleted, deleted_at, deleted_by, ext_json, content_completed, image_completed) VALUES (%(client_id)s, %(task_id)s, %(batch_id)s, %(template_id)s, %(title)s, %(content)s, %(topics)s, %(location)s, %(image_urls)s, %(display_date)s, %(display_time)s, %(workflow_status)s, %(publish_status)s, %(client_review_status)s, %(internal_review_status)s, %(publish_priority)s, %(publish_time)s, %(publish_error)s, %(publish_retry_count)s, %(status_update_time)s, %(created_at)s, %(updated_at)s, %(created_by)s, %(reviewer_id)s, %(review_time)s, %(image_editor_id)s, %(content_editor_id)s, %(is_deleted)s, %(deleted_at)s, %(deleted_by)s, %(ext_json)s, %(content_completed)s, %(image_completed)s)
2025-07-30 17:08:03,893 INFO sqlalchemy.engine.Engine [cached since 78.87s ago] {'client_id': 1, 'task_id': 9, 'batch_id': 16, 'template_id': 97, 'title': '深夜放毒时间到！✅ eett太罪恶了✅ ', 'conten
t': '✅ rr\r\n半夜饿到不行发现ee还营业！✅ tt热乎乎的超治愈，减肥什么的明天再说啦～ #夜宵美食 #深夜食
堂', 'topics': '["ee", "rr", "tt", "yy", "jj", "ll", "bb", "ee", "rr", "bb"]', 'location': 'rr', 'image_urls': None, 'display_date': datetime.date(2025, 7, 30), 'display_time': datetime.time(8, 50), 'workflow_status': 'draft', 'publish_status': 'unpublished', 'client_review_status': 'pending', 'internal_review_status': 'pending', 'publish_priority': 'normal', 'publish_time': None, 'publish_error': None, 'publish_retry_count': 0, 'status_update_time': datetime.datetime(2025, 7, 30, 17, 8, 3, 892047), 'created_at': datetime.datetime(2025, 7, 30, 17, 8, 3, 892047), 'updated_at': datetime.datetime(2025, 7, 30, 17, 8, 3, 892047), 'created_by': 1, 'reviewer_id': None, 'review_time': None, 'image_editor_id': 3, 'content_editor_id': 2, 'is_deleted': 0, 'deleted_at': None, 'deleted_by': None, 'ext_json': '{"at_users": ["@yy"]}', 'content_completed': 1, 'image_completed': 1}
2025-07-30 17:08:03,907 INFO sqlalchemy.engine.Engine COMMIT
2025-07-30 17:08:03,915 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-30 17:08:03,915 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1
FROM (SELECT contents.id AS contents_id, contents.client_id AS contents_client_id, contents.task_id AS contents_task_id, contents.batch_id AS contents_batch_id, contents.template_id AS contents_template_id, contents.title AS contents_title, contents.content AS contents_content, contents.topics AS contents_topics, contents.location AS contents_location, contents.image_urls AS contents_image_urls, contents.display_date AS contents_display_date, contents.display_time AS contents_display_time, 
contents.workflow_status AS contents_workflow_status, contents.publish_status AS contents_publish_status, contents.client_review_status AS contents_client_review_status, contents.internal_review_status AS contents_internal_review_status, contents.publish_priority AS contents_publish_priority, contents.publish_time AS contents_publish_time, contents.publish_error AS contents_publish_error, contents.publish_retry_count AS contents_publish_retry_count, contents.status_update_time AS contents_status_update_time, contents.created_at AS contents_created_at, contents.updated_at AS contents_updated_at, contents.created_by AS contents_created_by, contents.reviewer_id AS contents_reviewer_id, contents.review_time AS contents_review_time, contents.image_editor_id AS contents_image_editor_id, 
contents.content_editor_id AS contents_content_editor_id, contents.is_deleted AS contents_is_deleted, contents.deleted_at AS contents_deleted_at, contents.deleted_by AS contents_deleted_by, contents.ext_json AS contents_ext_json, contents.content_completed AS contents_content_completed, contents.image_completed AS contents_image_completed
FROM contents
WHERE contents.batch_id = %(batch_id_1)s) AS anon_1
2025-07-30 17:08:03,916 INFO sqlalchemy.engine.Engine [cached since 78.87s ago] {'batch_id_1': 16} 
2025-07-30 17:08:03,917 INFO sqlalchemy.engine.Engine SELECT batches.id AS batches_id, batches.task_id AS batches_task_id, batches.name AS batches_name, batches.content_count AS batches_content_count, batches.created_at AS batches_created_at, batches.created_by AS batches_created_by
FROM batches
WHERE batches.id = %(pk_1)s
2025-07-30 17:08:03,918 INFO sqlalchemy.engine.Engine [cached since 78.87s ago] {'pk_1': 16}       
2025-07-30 17:08:03,919 INFO sqlalchemy.engine.Engine UPDATE batches SET content_count=%(content_count)s WHERE batches.id = %(batches_id)s
2025-07-30 17:08:03,919 INFO sqlalchemy.engine.Engine [cached since 78.86s ago] {'content_count': 2, 'batches_id': 16}
2025-07-30 17:08:03,919 INFO sqlalchemy.engine.Engine COMMIT
DEBUG - generate_contents返回结果: 2 篇文案
DEBUG - 数据库事务提交成功
2025-07-30 17:08:03,926 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-30 17:08:03,927 INFO sqlalchemy.engine.Engine SELECT batches.id AS batches_id, batches.task_id AS batches_task_id, batches.name AS batches_name, batches.content_count AS batches_content_count, batches.created_at AS batches_created_at, batches.created_by AS batches_created_by
FROM batches
WHERE batches.id = %(pk_1)s
2025-07-30 17:08:03,927 INFO sqlalchemy.engine.Engine [cached since 78.88s ago] {'pk_1': 16}       
DEBUG - 返回AJAX响应: {'success': True, 'message': '文案生成成功！共生成了 2 篇文案，可以到初审文案
页面查看生成的内容', 'clear_form': True, 'generated_count': 2, 'batch_id': 16, 'task_id': 9}       
2025-07-30 17:08:03,928 INFO sqlalchemy.engine.Engine ROLLBACK
127.0.0.1 - - [30/Jul/2025 17:08:03] "POST /simple/content HTTP/1.1" 200 -
2025-07-30 17:08:05,851 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-30 17:08:05,851 INFO sqlalchemy.engine.Engine SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.email AS users_email, users.real_name AS users_real_name, users.phone AS users_phone, users.is_active AS users_is_active, users.created_at AS users_created_at, users.last_login AS users_last_login
FROM users
WHERE users.id = %(pk_1)s
2025-07-30 17:08:05,851 INFO sqlalchemy.engine.Engine [cached since 178s ago] {'pk_1': 1}
DEBUG - Loading user admin with ID 1
2025-07-30 17:08:05,852 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:08:05,853 INFO sqlalchemy.engine.Engine [cached since 178s ago] {'param_1': 1}       
DEBUG - User roles: ['超级管理员']
2025-07-30 17:08:05,854 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id   
2025-07-30 17:08:05,855 INFO sqlalchemy.engine.Engine [cached since 178s ago] {'param_1': 2}       
DEBUG - Role 超级管理员 permissions: ['dashboard.view', 'content.final_review', 'publish.manage', 'image.upload', 'template.manage', 'system.settings', 'content.review', 'publish.status', 'client.manage', 'user.manage', 'content.generate', 'client.review']
2025-07-30 17:08:05,856 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items
WHERE menu_items.is_active = true ORDER BY menu_items.`order`
2025-07-30 17:08:05,857 INFO sqlalchemy.engine.Engine [cached since 178s ago] {}
2025-07-30 17:08:05,859 INFO sqlalchemy.engine.Engine SELECT clients.id AS clients_id, clients.name AS clients_name, clients.contact AS clients_contact, clients.phone AS clients_phone, clients.email AS clients_email, clients.need_review AS clients_need_review, clients.daily_content_count AS clients_daily_content_count, clients.display_start_time AS clients_display_start_time, clients.interval_min AS clients_interval_min, clients.interval_max AS clients_interval_max, clients.review_timeout_hours AS clients_review_timeout_hours, clients.review_deadline_time AS clients_review_deadline_time, clients.auto_approve_enabled AS clients_auto_approve_enabled, clients.created_at AS clients_created_at, clients.updated_at AS clients_updated_at, clients.status AS clients_status, clients.ext_json 
AS clients_ext_json, clients.default_required_topics AS clients_default_required_topics, clients.default_random_topics AS clients_default_random_topics, clients.default_at_users AS clients_default_at_users, clients.default_location AS clients_default_location
FROM clients
WHERE clients.status = true
2025-07-30 17:08:05,859 INFO sqlalchemy.engine.Engine [cached since 170.8s ago] {}
2025-07-30 17:08:05,860 INFO sqlalchemy.engine.Engine SELECT template_categories.id AS template_categories_id, template_categories.name AS template_categories_name, template_categories.parent_id AS 
template_categories_parent_id, template_categories.sort_order AS template_categories_sort_order, template_categories.created_at AS template_categories_created_at, template_categories.updated_at AS template_categories_updated_at
FROM template_categories
2025-07-30 17:08:05,861 INFO sqlalchemy.engine.Engine [cached since 170.8s ago] {}
2025-07-30 17:08:05,862 INFO sqlalchemy.engine.Engine SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.email AS users_email, users.real_name AS users_real_name, users.phone AS users_phone, users.is_active AS users_is_active, users.created_at AS users_created_at, users.last_login AS users_last_login
FROM users
WHERE users.is_active = true
2025-07-30 17:08:05,862 INFO sqlalchemy.engine.Engine [cached since 170.8s ago] {}
2025-07-30 17:08:05,863 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id2025-07-30 17:08:05,863 INFO sqlalchemy.engine.Engine [cached since 175.4s ago] {'param_1': 1}     
2025-07-30 17:08:05,865 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id2025-07-30 17:08:05,865 INFO sqlalchemy.engine.Engine [cached since 175.4s ago] {'param_1': 2}     
2025-07-30 17:08:05,866 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id2025-07-30 17:08:05,866 INFO sqlalchemy.engine.Engine [cached since 175.4s ago] {'param_1': 3}     
2025-07-30 17:08:05,867 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id2025-07-30 17:08:05,867 INFO sqlalchemy.engine.Engine [cached since 175.4s ago] {'param_1': 4}     
2025-07-30 17:08:05,868 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id2025-07-30 17:08:05,869 INFO sqlalchemy.engine.Engine [cached since 175.4s ago] {'param_1': 5}
2025-07-30 17:08:05,870 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id2025-07-30 17:08:05,870 INFO sqlalchemy.engine.Engine [cached since 175.4s ago] {'param_1': 6}     
2025-07-30 17:08:05,871 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id2025-07-30 17:08:05,872 INFO sqlalchemy.engine.Engine [cached since 175.4s ago] {'param_1': 7}     
2025-07-30 17:08:05,873 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items, user_menu_permissions
WHERE %(param_1)s = user_menu_permissions.user_id AND menu_items.id = user_menu_permissions.menu_id2025-07-30 17:08:05,873 INFO sqlalchemy.engine.Engine [cached since 175.4s ago] {'param_1': 13}    
2025-07-30 17:08:05,874 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:08:05,874 INFO sqlalchemy.engine.Engine [cached since 178s ago] {'param_1': 2}       
2025-07-30 17:08:05,875 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:08:05,876 INFO sqlalchemy.engine.Engine [cached since 178s ago] {'param_1': 7}       
2025-07-30 17:08:05,876 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:08:05,877 INFO sqlalchemy.engine.Engine [cached since 178s ago] {'param_1': 13}      
2025-07-30 17:08:05,877 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, user_permissions
WHERE %(param_1)s = user_permissions.user_id AND permissions.id = user_permissions.permission_id   
2025-07-30 17:08:05,878 INFO sqlalchemy.engine.Engine [cached since 170.8s ago] {'param_1': 2}     
2025-07-30 17:08:05,878 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id   
2025-07-30 17:08:05,879 INFO sqlalchemy.engine.Engine [cached since 178s ago] {'param_1': 3}       
2025-07-30 17:08:05,879 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:08:05,879 INFO sqlalchemy.engine.Engine [cached since 178s ago] {'param_1': 3}       
2025-07-30 17:08:05,880 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, user_permissions
WHERE %(param_1)s = user_permissions.user_id AND permissions.id = user_permissions.permission_id   
2025-07-30 17:08:05,880 INFO sqlalchemy.engine.Engine [cached since 170.8s ago] {'param_1': 3}     
2025-07-30 17:08:05,881 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:08:05,881 INFO sqlalchemy.engine.Engine [cached since 178s ago] {'param_1': 4}       
2025-07-30 17:08:05,882 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, user_permissions
WHERE %(param_1)s = user_permissions.user_id AND permissions.id = user_permissions.permission_id   
2025-07-30 17:08:05,882 INFO sqlalchemy.engine.Engine [cached since 170.8s ago] {'param_1': 4}     
2025-07-30 17:08:05,882 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id   
2025-07-30 17:08:05,883 INFO sqlalchemy.engine.Engine [cached since 178s ago] {'param_1': 8}       
2025-07-30 17:08:05,883 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:08:05,883 INFO sqlalchemy.engine.Engine [cached since 178s ago] {'param_1': 5}       
2025-07-30 17:08:05,884 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, user_permissions
WHERE %(param_1)s = user_permissions.user_id AND permissions.id = user_permissions.permission_id   
2025-07-30 17:08:05,884 INFO sqlalchemy.engine.Engine [cached since 170.8s ago] {'param_1': 5}     
2025-07-30 17:08:05,885 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id   
2025-07-30 17:08:05,885 INFO sqlalchemy.engine.Engine [cached since 178s ago] {'param_1': 6}       
2025-07-30 17:08:05,886 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:08:05,886 INFO sqlalchemy.engine.Engine [cached since 178s ago] {'param_1': 6}       
2025-07-30 17:08:05,886 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, user_permissions
WHERE %(param_1)s = user_permissions.user_id AND permissions.id = user_permissions.permission_id   
2025-07-30 17:08:05,887 INFO sqlalchemy.engine.Engine [cached since 170.8s ago] {'param_1': 6}     
2025-07-30 17:08:05,887 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id   
2025-07-30 17:08:05,887 INFO sqlalchemy.engine.Engine [cached since 178s ago] {'param_1': 5}       
2025-07-30 17:08:05,888 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, user_permissions
WHERE %(param_1)s = user_permissions.user_id AND permissions.id = user_permissions.permission_id   
2025-07-30 17:08:05,889 INFO sqlalchemy.engine.Engine [cached since 170.8s ago] {'param_1': 7}     
2025-07-30 17:08:05,890 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, user_permissions
WHERE %(param_1)s = user_permissions.user_id AND permissions.id = user_permissions.permission_id   
2025-07-30 17:08:05,890 INFO sqlalchemy.engine.Engine [cached since 170.8s ago] {'param_1': 13}    
2025-07-30 17:08:05,891 INFO sqlalchemy.engine.Engine SELECT tasks.id AS tasks_id, tasks.client_id 
AS tasks_client_id, tasks.name AS tasks_name, tasks.description AS tasks_description, tasks.status 
AS tasks_status, tasks.target_count AS tasks_target_count, tasks.actual_count AS tasks_actual_count, tasks.created_at AS tasks_created_at, tasks.updated_at AS tasks_updated_at, tasks.created_by AS tasks_created_by
FROM tasks
WHERE tasks.client_id = %(client_id_1)s
2025-07-30 17:08:05,891 INFO sqlalchemy.engine.Engine [cached since 170.8s ago] {'client_id_1': 1} 
DEBUG - 活跃用户数量: 8
DEBUG - 有初审权限用户数量: 4
DEBUG - 有图片上传权限用户数量: 2
DEBUG - 文案编辑选择项: [(2, '审核张三'), (7, '模板管理员'), (13, '审核李四'), (1, '管理员')]      
DEBUG - 图片编辑选择项: [(3, '图文编辑'), (1, '管理员')]
2025-07-30 17:08:05,894 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items
WHERE menu_items.is_active = true ORDER BY menu_items.`order`
2025-07-30 17:08:05,895 INFO sqlalchemy.engine.Engine [cached since 178s ago] {}
2025-07-30 17:08:05,897 INFO sqlalchemy.engine.Engine ROLLBACK
127.0.0.1 - - [30/Jul/2025 17:08:05] "GET /simple/content HTTP/1.1" 200 -
2025-07-30 17:08:05,930 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-30 17:08:05,930 INFO sqlalchemy.engine.Engine SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.email AS users_email, users.real_name AS users_real_name, users.phone AS users_phone, users.is_active AS users_is_active, users.created_at AS users_created_at, users.last_login AS users_last_login
FROM users
WHERE users.id = %(pk_1)s
2025-07-30 17:08:05,931 INFO sqlalchemy.engine.Engine [cached since 178.1s ago] {'pk_1': 1}        
DEBUG - Loading user admin with ID 1
2025-07-30 17:08:05,932 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:08:05,932 INFO sqlalchemy.engine.Engine [cached since 178.1s ago] {'param_1': 1}     
DEBUG - User roles: ['超级管理员']
2025-07-30 17:08:05,933 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id   
2025-07-30 17:08:05,934 INFO sqlalchemy.engine.Engine [cached since 178.1s ago] {'param_1': 2}     
DEBUG - Role 超级管理员 permissions: ['dashboard.view', 'content.final_review', 'publish.manage', 'image.upload', 'template.manage', 'system.settings', 'content.review', 'publish.status', 'client.manage', 'user.manage', 'content.generate', 'client.review']
2025-07-30 17:08:05,935 INFO sqlalchemy.engine.Engine SELECT menu_items.id AS menu_items_id, menu_items.name AS menu_items_name, menu_items.url AS menu_items_url, menu_items.icon AS menu_items_icon, menu_items.permission AS menu_items_permission, menu_items.parent_id AS menu_items_parent_id, menu_items.`order` AS menu_items_order, menu_items.is_active AS menu_items_is_active, menu_items.created_at AS menu_items_created_at, menu_items.updated_at AS menu_items_updated_at
FROM menu_items
WHERE menu_items.is_active = true ORDER BY menu_items.`order`
2025-07-30 17:08:05,936 INFO sqlalchemy.engine.Engine [cached since 178.1s ago] {}
127.0.0.1 - - [30/Jul/2025 17:08:05] "GET /static/js/bootstrap-simple.min.js HTTP/1.1" 304 -       
2025-07-30 17:08:05,940 INFO sqlalchemy.engine.Engine ROLLBACK
127.0.0.1 - - [30/Jul/2025 17:08:05] "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 -
127.0.0.1 - - [30/Jul/2025 17:08:06] "GET /favicon.ico HTTP/1.1" 304 -
2025-07-30 17:08:06,918 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-30 17:08:06,918 INFO sqlalchemy.engine.Engine SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.email AS users_email, users.real_name AS users_real_name, users.phone AS users_phone, users.is_active AS users_is_active, users.created_at AS users_created_at, users.last_login AS users_last_login
FROM users
WHERE users.id = %(pk_1)s
2025-07-30 17:08:06,919 INFO sqlalchemy.engine.Engine [cached since 179s ago] {'pk_1': 1}
2025-07-30 17:08:06,919 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-30 17:08:06,920 INFO sqlalchemy.engine.Engine SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.email AS users_email, users.real_name AS users_real_name, users.phone AS users_phone, users.is_active AS users_is_active, users.created_at AS users_created_at, users.last_login AS users_last_login
FROM users
WHERE users.id = %(pk_1)s
DEBUG - Loading user admin with ID 1
2025-07-30 17:08:06,921 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:08:06,921 INFO sqlalchemy.engine.Engine [cached since 179.1s ago] {'pk_1': 1}        
2025-07-30 17:08:06,921 INFO sqlalchemy.engine.Engine [cached since 179s ago] {'param_1': 1}       
DEBUG - Loading user admin with ID 1
DEBUG - User roles: ['超级管理员']
2025-07-30 17:08:06,923 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:08:06,923 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id   
2025-07-30 17:08:06,924 INFO sqlalchemy.engine.Engine [cached since 179s ago] {'param_1': 1}       
2025-07-30 17:08:06,924 INFO sqlalchemy.engine.Engine [cached since 179s ago] {'param_1': 2}       
DEBUG - User roles: ['超级管理员']
DEBUG - Role 超级管理员 permissions: ['dashboard.view', 'content.final_review', 'publish.manage', 'image.upload', 'template.manage', 'system.settings', 'content.review', 'publish.status', 'client.manage', 'user.manage', 'content.generate', 'client.review']
2025-07-30 17:08:06,926 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id   
2025-07-30 17:08:06,926 INFO sqlalchemy.engine.Engine SELECT clients.id AS clients_id, clients.name AS clients_name, clients.contact AS clients_contact, clients.phone AS clients_phone, clients.email AS clients_email, clients.need_review AS clients_need_review, clients.daily_content_count AS clients_daily_content_count, clients.display_start_time AS clients_display_start_time, clients.interval_min AS clients_interval_min, clients.interval_max AS clients_interval_max, clients.review_timeout_hours AS clients_review_timeout_hours, clients.review_deadline_time AS clients_review_deadline_time, clients.auto_approve_enabled AS clients_auto_approve_enabled, clients.created_at AS clients_created_at, clients.updated_at AS clients_updated_at, clients.status AS clients_status, clients.ext_json 
AS clients_ext_json, clients.default_required_topics AS clients_default_required_topics, clients.default_random_topics AS clients_default_random_topics, clients.default_at_users AS clients_default_at_users, clients.default_location AS clients_default_location
FROM clients
WHERE clients.id = %(pk_1)s
2025-07-30 17:08:06,927 INFO sqlalchemy.engine.Engine [cached since 179s ago] {'param_1': 2}       
2025-07-30 17:08:06,927 INFO sqlalchemy.engine.Engine [cached since 126.9s ago] {'pk_1': 1}        
2025-07-30 17:08:06,929 INFO sqlalchemy.engine.Engine ROLLBACK
DEBUG - Role 超级管理员 permissions: ['dashboard.view', 'content.final_review', 'publish.manage', 'image.upload', 'template.manage', 'system.settings', 'content.review', 'publish.status', 'client.manage', 'user.manage', 'content.generate', 'client.review']
2025-07-30 17:08:06,930 INFO sqlalchemy.engine.Engine SELECT tasks.id AS tasks_id, tasks.client_id 
AS tasks_client_id, tasks.name AS tasks_name, tasks.description AS tasks_description, tasks.status 
AS tasks_status, tasks.target_count AS tasks_target_count, tasks.actual_count AS tasks_actual_count, tasks.created_at AS tasks_created_at, tasks.updated_at AS tasks_updated_at, tasks.created_by AS tasks_created_by
FROM tasks
WHERE tasks.client_id = %(client_id_1)s
127.0.0.1 - - [30/Jul/2025 17:08:06] "GET /simple/api/clients/1/defaults HTTP/1.1" 200 -
2025-07-30 17:08:06,931 INFO sqlalchemy.engine.Engine [cached since 171.8s ago] {'client_id_1': 1} 
2025-07-30 17:08:06,933 INFO sqlalchemy.engine.Engine ROLLBACK
127.0.0.1 - - [30/Jul/2025 17:08:06] "GET /simple/contents/get-tasks/1 HTTP/1.1" 200 -
2025-07-30 17:08:06,958 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-30 17:08:06,959 INFO sqlalchemy.engine.Engine SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.email AS users_email, users.real_name AS users_real_name, users.phone AS users_phone, users.is_active AS users_is_active, users.created_at AS users_created_at, users.last_login AS users_last_login
FROM users
WHERE users.id = %(pk_1)s
2025-07-30 17:08:06,959 INFO sqlalchemy.engine.Engine [cached since 179.1s ago] {'pk_1': 1}        
DEBUG - Loading user admin with ID 1
2025-07-30 17:08:06,961 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:08:06,962 INFO sqlalchemy.engine.Engine [cached since 179.1s ago] {'param_1': 1}     
DEBUG - User roles: ['超级管理员']
2025-07-30 17:08:06,963 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id   
2025-07-30 17:08:06,963 INFO sqlalchemy.engine.Engine [cached since 179.1s ago] {'param_1': 2}     
DEBUG - Role 超级管理员 permissions: ['dashboard.view', 'content.final_review', 'publish.manage', 'image.upload', 'template.manage', 'system.settings', 'content.review', 'publish.status', 'client.manage', 'user.manage', 'content.generate', 'client.review']
2025-07-30 17:08:06,964 INFO sqlalchemy.engine.Engine SELECT tasks.id AS tasks_id, tasks.client_id 
AS tasks_client_id, tasks.name AS tasks_name, tasks.description AS tasks_description, tasks.status 
AS tasks_status, tasks.target_count AS tasks_target_count, tasks.actual_count AS tasks_actual_count, tasks.created_at AS tasks_created_at, tasks.updated_at AS tasks_updated_at, tasks.created_by AS tasks_created_by
FROM tasks
WHERE tasks.client_id = %(client_id_1)s AND tasks.name = %(name_1)s
 LIMIT %(param_1)s
2025-07-30 17:08:06,964 INFO sqlalchemy.engine.Engine [cached since 126.9s ago] {'client_id_1': 1, 
'name_1': '2025年07月30日任务', 'param_1': 1}
2025-07-30 17:08:06,965 INFO sqlalchemy.engine.Engine SELECT count(*) AS count_1
FROM (SELECT batches.id AS batches_id, batches.task_id AS batches_task_id, batches.name AS batches_name, batches.content_count AS batches_content_count, batches.created_at AS batches_created_at, batches.created_by AS batches_created_by
FROM batches
WHERE batches.task_id = %(task_id_1)s) AS anon_1
2025-07-30 17:08:06,965 INFO sqlalchemy.engine.Engine [cached since 126.9s ago] {'task_id_1': 8}   
2025-07-30 17:08:06,966 INFO sqlalchemy.engine.Engine ROLLBACK
127.0.0.1 - - [30/Jul/2025 17:08:06] "POST /simple/contents/check-task-name HTTP/1.1" 200 -        
2025-07-30 17:08:07,061 INFO sqlalchemy.engine.Engine BEGIN (implicit)
2025-07-30 17:08:07,061 INFO sqlalchemy.engine.Engine SELECT users.id AS users_id, users.username AS users_username, users.password_hash AS users_password_hash, users.email AS users_email, users.real_name AS users_real_name, users.phone AS users_phone, users.is_active AS users_is_active, users.created_at AS users_created_at, users.last_login AS users_last_login
FROM users
WHERE users.id = %(pk_1)s
2025-07-30 17:08:07,061 INFO sqlalchemy.engine.Engine [cached since 179.2s ago] {'pk_1': 1}        
DEBUG - Loading user admin with ID 1
2025-07-30 17:08:07,063 INFO sqlalchemy.engine.Engine SELECT roles.id AS roles_id, roles.name AS roles_name, roles.description AS roles_description, roles.created_at AS roles_created_at
FROM roles, user_roles
WHERE %(param_1)s = user_roles.user_id AND roles.id = user_roles.role_id
2025-07-30 17:08:07,063 INFO sqlalchemy.engine.Engine [cached since 179.2s ago] {'param_1': 1}     
DEBUG - User roles: ['超级管理员']
2025-07-30 17:08:07,064 INFO sqlalchemy.engine.Engine SELECT permissions.id AS permissions_id, permissions.name AS permissions_name, permissions.description AS permissions_description
FROM permissions, role_permissions
WHERE %(param_1)s = role_permissions.role_id AND permissions.id = role_permissions.permission_id   
2025-07-30 17:08:07,064 INFO sqlalchemy.engine.Engine [cached since 179.2s ago] {'param_1': 2}     
DEBUG - Role 超级管理员 permissions: ['dashboard.view', 'content.final_review', 'publish.manage', 'image.upload', 'template.manage', 'system.settings', 'content.review', 'publish.status', 'client.manage', 'user.manage', 'content.generate', 'client.review']
=== 开始验证生成数量 ===
Content-Type: application/json
Request method: POST
原始请求数据: {'template_category_id': '1', 'client_id': '1', 'task_id': '0', 'task_name': '2025年07月30日任务', 'duplicate_control': 'task', 'requested_count': 1}
解析后的参数: category_id=1, client_id=1, task_id=0, task_name=2025年07月30日任务, duplicate_control=task, requested_count=1
转换后的参数: category_id=1, client_id=1, task_id=None
查询模板分类: 1
2025-07-30 17:08:07,065 INFO sqlalchemy.engine.Engine SELECT template_categories.id AS template_categories_id, template_categories.name AS template_categories_name, template_categories.parent_id AS 
template_categories_parent_id, template_categories.sort_order AS template_categories_sort_order, template_categories.created_at AS template_categories_created_at, template_categories.updated_at AS template_categories_updated_at
FROM template_categories
WHERE template_categories.id = %(pk_1)s
2025-07-30 17:08:07,066 INFO sqlalchemy.engine.Engine [cached since 126.9s ago] {'pk_1': 1}        
找到模板分类: 美妆护肤
查询分类下的模板...
2025-07-30 17:08:07,067 INFO sqlalchemy.engine.Engine SELECT templates.id AS templates_id, templates.category_id AS templates_category_id, templates.title AS templates_title, templates.content AS templates_content, templates.marks AS templates_marks, templates.creator_id AS templates_creator_id, 
templates.created_at AS templates_created_at, templates.updated_at AS templates_updated_at, templates.status AS templates_status
FROM templates
WHERE templates.category_id = %(category_id_1)s AND templates.status = true
2025-07-30 17:08:07,067 INFO sqlalchemy.engine.Engine [cached since 126.9s ago] {'category_id_1': 1}
找到 2 个模板
找到 3 个标记，用户需要填写 3 个标记
用户标记: ['商品名称', '店铺地址', '品牌名称']
重复性控制: task
任务不重复模式，task_id=0，检查同名任务
使用前端传递的任务名称: 2025年07月30日任务
检查同名任务: 2025年07月30日任务
2025-07-30 17:08:07,069 INFO sqlalchemy.engine.Engine SELECT tasks.id AS tasks_id, tasks.client_id 
AS tasks_client_id, tasks.name AS tasks_name, tasks.description AS tasks_description, tasks.status 
AS tasks_status, tasks.target_count AS tasks_target_count, tasks.actual_count AS tasks_actual_count, tasks.created_at AS tasks_created_at, tasks.updated_at AS tasks_updated_at, tasks.created_by AS tasks_created_by
FROM tasks
WHERE tasks.client_id = %(client_id_1)s AND tasks.name = %(name_1)s
 LIMIT %(param_1)s
2025-07-30 17:08:07,069 INFO sqlalchemy.engine.Engine [cached since 127s ago] {'client_id_1': 1, 'name_1': '2025年07月30日任务', 'param_1': 1}
找到同名任务: id=8, name=2025年07月30日任务
模板IDs: [95, 96]
2025-07-30 17:08:07,071 INFO sqlalchemy.engine.Engine SELECT DISTINCT contents.template_id AS contents_template_id
FROM contents
WHERE contents.task_id = %(task_id_1)s AND contents.template_id IN (%(template_id_1_1)s, %(template_id_1_2)s) AND contents.is_deleted != true
2025-07-30 17:08:07,071 INFO sqlalchemy.engine.Engine [cached since 126.9s ago] {'task_id_1': 8, 'template_id_1_1': 95, 'template_id_1_2': 96}
同名任务已使用模板: [95, 96]
可用模板数: 0, 可生成数量: 0
返回结果: {'success': True, 'category_name': '美妆护肤', 'total_templates': 2, 'usable_templates': 
0, 'available_count': 0, 'marks_count': 3, 'user_marks': ['商品名称', '店铺地址', '品牌名称'], 'duplicate_info': '该任务已使用 2 个模板', 'requested_count': 1}
=== 验证生成数量完成 ===
2025-07-30 17:08:07,074 INFO sqlalchemy.engine.Engine ROLLBACK
127.0.0.1 - - [30/Jul/2025 17:08:07] "POST /simple/validate-generation-count HTTP/1.1" 200 -  