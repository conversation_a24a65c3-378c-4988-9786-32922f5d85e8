#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查当前用户角色
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app
    from app.models import db
    from app.models.user import User
    print("✅ 模块导入成功")
except Exception as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

def check_user_roles():
    """检查用户角色"""
    app = create_app()
    
    with app.app_context():
        try:
            print("=== 检查用户角色 ===")
            
            # 获取所有用户
            users = User.query.filter_by(is_active=True).all()
            
            for user in users:
                print(f"\n用户: {user.username} ({user.real_name or '未设置'})")
                print(f"  用户ID: {user.id}")
                print(f"  是否活跃: {user.is_active}")
                print(f"  是否管理员: {user.is_admin}")
                
                # 检查角色
                roles = [role.name for role in user.roles]
                print(f"  角色列表: {roles}")
                
                # 检查是否有超级管理员角色
                has_super_admin = user.has_role('超级管理员')
                print(f"  是否超级管理员: {'是' if has_super_admin else '否'}")
                
                # 检查权限
                has_content_review = user.has_permission('content.review')
                print(f"  是否有初审权限: {'是' if has_content_review else '否'}")
            
            print("\n=== 角色定义检查 ===")
            from app.models.user import Role
            roles = Role.query.all()
            for role in roles:
                print(f"角色: {role.name} - {role.description}")
            
            print("\n=== 检查完成 ===")
            
        except Exception as e:
            print(f"❌ 检查过程中出错: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    check_user_roles()
