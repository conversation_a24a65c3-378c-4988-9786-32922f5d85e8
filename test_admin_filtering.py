#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试管理员过滤功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from app import create_app
    from app.models import db
    from app.models.user import User
    from app.models.content import Content
    from app.models.client import Client
    print("✅ 模块导入成功")
except Exception as e:
    print(f"❌ 模块导入失败: {e}")
    exit(1)

def test_admin_filtering():
    """测试管理员过滤功能"""
    app = create_app()
    
    with app.app_context():
        try:
            print("=== 测试管理员过滤功能 ===")
            
            # 获取管理员用户
            admin_user = User.query.filter_by(username='admin').first()
            reviewer_user = User.query.filter_by(username='reviewer').first()
            
            if not admin_user or not reviewer_user:
                print("❌ 找不到测试用户")
                return
            
            print(f"\n管理员用户: {admin_user.username} (ID: {admin_user.id})")
            print(f"是否超级管理员: {'是' if admin_user.has_role('超级管理员') else '否'}")
            
            print(f"\n审核员用户: {reviewer_user.username} (ID: {reviewer_user.id})")
            print(f"是否超级管理员: {'是' if reviewer_user.has_role('超级管理员') else '否'}")
            
            # 统计所有文案
            all_contents = Content.query.filter_by(is_deleted=False).all()
            print(f"\n系统中所有文案数量: {len(all_contents)}")
            
            # 统计分配给管理员的文案
            admin_contents = Content.query.filter(
                Content.content_editor_id == admin_user.id,
                Content.is_deleted == False
            ).all()
            print(f"分配给管理员的文案数量: {len(admin_contents)}")
            
            # 统计分配给审核员的文案
            reviewer_contents = Content.query.filter(
                Content.content_editor_id == reviewer_user.id,
                Content.is_deleted == False
            ).all()
            print(f"分配给审核员的文案数量: {len(reviewer_contents)}")
            
            # 模拟管理员的查询逻辑（新的过滤逻辑）
            print(f"\n=== 模拟管理员查询 ===")
            base_filters = [Content.is_deleted == False]
            # 新逻辑：所有用户（包括管理员）只能看到分配给自己的文案
            base_filters.append(Content.content_editor_id == admin_user.id)
            
            admin_visible_contents = Content.query.filter(*base_filters).all()
            print(f"管理员可见的文案数量: {len(admin_visible_contents)}")
            
            if admin_visible_contents:
                print(f"管理员可见的文案ID: {[c.id for c in admin_visible_contents[:5]]}{'...' if len(admin_visible_contents) > 5 else ''}")
            
            # 模拟审核员的查询逻辑
            print(f"\n=== 模拟审核员查询 ===")
            base_filters = [Content.is_deleted == False]
            base_filters.append(Content.content_editor_id == reviewer_user.id)
            
            reviewer_visible_contents = Content.query.filter(*base_filters).all()
            print(f"审核员可见的文案数量: {len(reviewer_visible_contents)}")
            
            if reviewer_visible_contents:
                print(f"审核员可见的文案ID: {[c.id for c in reviewer_visible_contents[:5]]}{'...' if len(reviewer_visible_contents) > 5 else ''}")
            
            # 测试客户统计
            print(f"\n=== 测试客户统计 ===")
            
            # 管理员的客户统计
            admin_clients = db.session.query(Client.id, Client.name).join(
                Content, Client.id == Content.client_id
            ).filter(
                Content.is_deleted == False,
                Content.content_editor_id == admin_user.id
            ).distinct().all()
            
            print(f"管理员可见的客户数量: {len(admin_clients)}")
            if admin_clients:
                print(f"管理员可见的客户: {[c.name for c in admin_clients]}")
            
            # 审核员的客户统计
            reviewer_clients = db.session.query(Client.id, Client.name).join(
                Content, Client.id == Content.client_id
            ).filter(
                Content.is_deleted == False,
                Content.content_editor_id == reviewer_user.id
            ).distinct().all()
            
            print(f"审核员可见的客户数量: {len(reviewer_clients)}")
            if reviewer_clients:
                print(f"审核员可见的客户: {[c.name for c in reviewer_clients]}")
            
            # 验证权限隔离
            print(f"\n=== 验证权限隔离 ===")
            
            if len(admin_visible_contents) == 0:
                print("✅ 管理员没有分配的文案，看不到任何数据")
            else:
                print(f"⚠️  管理员有 {len(admin_visible_contents)} 篇分配的文案")
            
            if len(reviewer_visible_contents) > 0:
                print(f"✅ 审核员有 {len(reviewer_visible_contents)} 篇分配的文案")
            else:
                print("⚠️  审核员没有分配的文案")
            
            # 检查是否有交集
            admin_ids = set(c.id for c in admin_visible_contents)
            reviewer_ids = set(c.id for c in reviewer_visible_contents)
            intersection = admin_ids & reviewer_ids
            
            if len(intersection) == 0:
                print("✅ 管理员和审核员的文案没有交集，权限隔离正常")
            else:
                print(f"❌ 管理员和审核员有共同的文案: {intersection}")
            
            print("\n=== 测试完成 ===")
            
        except Exception as e:
            print(f"❌ 测试过程中出错: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_admin_filtering()
